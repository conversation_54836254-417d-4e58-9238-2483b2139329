<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>CKFinder &mdash; Podręcznik Użytkownika</title>
	<link href="../../files/other/help.css" type="text/css" rel="stylesheet" />
	<script type="text/javascript" src="../../files/other/help.js"></script>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta name="robots" content="noindex, nofollow" />
</head>
<body>
	<h1>
		Przesyłanie plików</h1>
	<p>
		Przycisk <strong>Wyślij (Upload)</strong> dostępny w <strong><a href="005.html">pasku narzędzi</a>
		</strong> CKFindera otwiera <strong>panel przesyłania pliku</strong>, który można wykorzystać
		do dodania nowych plików do aktualnego folderu.</p>
	<p>Poniższy rysunek prezentuje domyślny <strong>panel przesyłania pliku</strong> CKFindera,
		który otwiera się po kliknięciu przycisku paska narzędzi.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_upload_01.png" width="591" height="145" alt="Panel przesyłania pliku CKFindera korzystający z technologii Flash" />&nbsp;</p>
	<p>
		W celu zamknięcia (ukrycia) <strong>panelu przesyłania pliku</strong> należy nacisnąć
		przycisk <strong>Anuluj (Cancel)</strong> (jeśli proces przesyłania plików jeszcze się
		nie rozpoczął) lub przycisk <strong>Zamknij (Close)</strong> (jeśli przesyłanie plików
		zostało zakończone). By ukryć panel, można także kliknąć ponownie przycisk
		<strong>Wyślij (Upload)</strong> na pasku narzędzi.</p>
	<p>
		<span class="info">Uwaga:</span> <em>Przesyłanie plików do serwera</em> to termin techniczny
		oznaczający wysyłanie plików z lokalnego komputera do komputera centralnego (znanego
		również jako <em>serwer</em>).</p>
	<h2>
		Wersje panelu przesyłania pliku</h2>
	<p><strong>Panel przesyłania pliku</strong> w CKFinderze występuje w dwóch wersjach,
		w zależności od środowiska, w jakim się pracuje. Domyślnie we wszystkich środowiskach
		obsługujących technologię Adobe Flash wykorzystywana jest opcja jednoczesnego
		przesyłania wielu plików (ang. <em>multiple file upload</em>), która pozwa wysłać większą
		liczbę plików do serwera w trakcie jednej operacji. Jeśli jednak Twój system nie obsługuje
		technologii Flash, będziesz mógł przesyłać pojedyncze pliki do serwera w odrębnych
		operacjach.</p>
	<p>Powyższy rysunek prezentuje domyślny <strong>panel przesyłania pliku</strong> obsługujący
		jednoczesne wysyłanie wielu plików. W przypadku systemów nieobsługujących technologii
		Flash (w tym przeglądarek mobilnych) wykorzystany zostanie następujący format panelu.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_upload_09.png" width="572" height="143" alt="Panel przesyłania pliku CKFindera dla pojedynczego pliku" />&nbsp;</p>
	<h2>
		Jednoczesne przesyłanie wielu plików</h2>
	<p>
		Domyślnie CKFinder pozwala przesłać do serwera wiele plików jednocześnie.
		W celu wysłania plików do serwera należy wykonać kroki opisane poniżej.</p>
	<p><em>Krok 1:</em> Kliknij przycisk <strong>Wyślij (Upload)</strong> na pasku
		narzędzi w celu otwarcia <strong>panelu przesyłania pliku</strong>.</p>
	<p><em>Krok 2:</em> Po otwraciu <strong>panelu przesyłania pliku</strong> wybierz
		przycisk <strong>Dodaj pliki (Add Files)</strong>. W oknie wyboru pliku Twojego
		systemu operacyjnego wskaż plik znajdujący się na Twoim komputerze, który chcesz
		przesłać do serwera.</p>
	<p><span class="info">Uwaga:</span> Możesz wskazać dowolną liczbę plików jednocześnie,
		wybierając je wszystkie naraz w oknie wyboru pliku.</p>
	<p><em>Krok 3:</em> Lokalne pliki zostaną teraz dodane do kolejki przesyłanych plików.
		Jeśli chcesz dodać kolejne pliki do kolejki, kliknij ponownie przycisk
		<strong>Dodaj pliki (Add Files)</strong> i powtórz czynności opisane wyżej.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_upload_03.png" width="590" height="283" alt="Kolejka przesyłanych plików w CKFinderze" />&nbsp;</p>
	<p><em>Krok 4:</em> Jeśli zmienisz zdanie w kwestii tego, które pliki powinny być
		przesłane do serwera, możesz w dowolnym momencie albo usunąć pojedynczy plik
		z kolejki, klikając przycisk <strong>Usuń (Remove)</strong> obok jego nazwy,
		albo anulować cały proces przesyłania za pomocą kliknięcia przycisku <strong>Anuluj (Cancel)</strong>
		w <strong>panelu przesyłania pliku</strong>.</p>
	<p><em>Krok 5:</em> Jeśli zakończyłeś proces wyboru plików, możesz kliknąć przycisk
		<strong>Wyślij (Upload)</strong> w celu rozpoczęcia procesu przesyłania plików do serwera.
		Postęp całego procesu, a także poszczególnych plików, można obserwować na bieżąco na ekranie.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_upload_04.png" width="575" height="289" alt="Postęp przesyłania wielu plików w CKFinderze" />&nbsp;</p>
	<p><em>Krok 6:</em> Poczekaj na zakończenie procesu przesyłania plików. Po jego zakończeniu
		zawartość folderu zostanie odświeżona, a ostatnio przesłany plik zostanie
		wybrany w <a href="004.html"><strong>panelu plików</strong></a>.</p>
	<h2>
		Przesyłanie pojedynczych plików</h2>
	<p>Jeśli technologia Adobe Flash nie jest obsługiwana w Twoim systemie, przesyłanie plików
		za pomocą CKFindera nadal będzie działało, choć będzie ograniczone do przesyłania
		jednego pliku jednorazowo. W celu przesłania pliku do serwera należy wykonać kroki
		opisane poniżej.</p>
	<p><em>Krok 1:</em> Kliknij przycisk <strong>Wyślij (Upload)</strong> na pasku narzędzi
		w celu otwarcia <strong>panelu przesyłania pliku</strong>.</p>
	<p><em>Krok 2:</em> Po otwarciu <strong>panelu przesyłania pliku</strong> wybierz przycisk
		<strong>Przeglądaj (Browse)</strong>. W oknie wyboru pliku Twojego
		systemu operacyjnego wskaż plik znajdujący się na Twoim komputerze, który chcesz
		przesłać do serwera.</p>
	<p><span class="info">Uwaga:</span> Podpis tego przycisku może się różnić w poszczególnych
		przeglądarkach internetowych.</p>
	<p><em>Krok 3:</em> Lokalny plik zostanie teraz wybrany. W tym wariancie możesz przesłać
		do serwera jeden plik jednocześnie.</p>
	<p><em>Krok 4:</em> Jeśli zakończyłeś proces wyboru pliku, możesz kliknąć przycisk
		<strong>Wyślij wybrany plik (Upload Selected File)</strong> w celu rozpoczęcia
		procesu przesyłania plików do serwera. Postęp całego procesu można obserwować na bieżąco
		na ekranie.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_upload_10.png" width="574" height="172" alt="Przesyłanie pojedynczego pliku do serwera" />&nbsp;</p>
	<p><em>Krok 5:</em> Poczekaj na zakończenie procesu przesyłania pliku. Po jego zakończeniu
		zawartość folderu zostanie odświeżona, a ostatnio przesłany plik (lub ostatni z kilku
		przesłanych plików) zostanie wybrany w <a href="004.html"><strong>panelu plików</strong></a>.</p>
	<h2>
		Błędy przesyłania plików do serwera</h2>
	<p>
		Następujące komunikaty o błędach mogą zostać wyświetlone po wysłaniu pliku.</p>
	<h3>
		Plik o podanej nazwie już istnieje. Nazwa przesłanego pliku została zmieniona na
		"<em>nazwa_pliku(1).ext</em>".</h3>
	<p>
		Komunikat ten wskazuje, że nazwa przesyłanego pliku jest już w użyciu przez inny
		plik znajdujący się w tym samym folderze. W celu uniknięcia konfliktu kolejny numer
		("1") został dodany do oryginalnej nazwy pliku.</p>
	<h3>
		Nieprawidłowy plik.</h3>
	<p>
		Plik, który próbowałeś przesłać do serwera, nie został zaakceptowany.</p>
	<p>
		Najczęstszym powodem tej sytuacji jest takie skonfigurowanie CKFindera przez administratora,
		w którym dozwolone jest przesyłanie plików jedynie z wybranymi rozszerzeniami.
		Rozwiązanie to ma na celu zabezpieczenie serwera przed wysłaniem niedozwolonych plików.
		Innym powodem może być przekroczenie dozwolonego rozmiaru pliku przesyłanego do serwera.
		W takim przypadku serwer powinien zostać skonfigurowany przez administratora w taki sposób,
		aby dopuszczał pliki o większych rozmiarach.</p>
	<h3>Przesyłanie pliku zakończyło się niepowodzeniem z powodów bezpieczeństwa. Plik zawiera dane
		przypominające HTML.</h3>
	<p>Przesłany plik zawiera kod HTML. Z powodów bezpieczeństwa tylko pliki z wybranymi rozszerzeniami
		mogą zawierać kod HTML.</p>
	<p>Prosimy o kontakt z administratorem w celu uzyskania informacji o plikach, które są akceptowane
		przez CKFindera, oraz o dopuszczalnym limicie rozmiaru pojedynczego pliku.</p>
</body>
</html>
