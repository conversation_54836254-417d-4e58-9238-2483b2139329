<?php

function countLots($codigo)
{
    $modelLeilao = new \App\Leiloes;
    $leilao = $modelLeilao::find($codigo);
    if ($leilao) {
        return $leilao->lots()->count();
    }
    return 0;
}

function checkValidDate($date)
{
    $date = trim($date);
    if ($date == '') {
        return false;
    }
    if (strlen($date) <  10 || strlen($date) > 10) {
        return false;
    }
    $explodeDate = explode('/', $date);
    if (count($explodeDate) < 3) {
        return false;
    }
    return checkdate($explodeDate[1], $explodeDate[0], $explodeDate[2]);
}

function pager($pagina, $sql, $post, $bind, $search = '') {

    $numLista = 12;
    $leiloes = \Illuminate\Support\Facades\DB::select($sql, $bind);

    $num = $leiloes[0]->conta;

    if (!$pagina) {
        $pagina = 1;
    }
    $inicio = $pagina - 1;
    $inicio = $numLista * $inicio;

    //calculando pagina anterior
    $menos = $pagina - 1;

    //calculando pagina posterior
    $mais = $pagina + 1;

    $pgs = ceil($num / $numLista);

    $idestado = isset($input['idestado']) ? $input['idestado'] : '';
    $idcidade = isset($input['idcidade']) ? $input['idcidade'] : '';
    $valor = isset($input['valor']) ? $input['valor'] : '';
    $categoria = isset($input['idcategoria']) ? $input['idcategoria'] : '';
    $paginator =  '';
    $searchUri = '';
    if ($search !== '' ) {
        $searchUri = '&search=' . $search;
    }
    if($pgs > 1)
    {
        if($menos > 0)
        {
            $paginator .= '<a href="'.$post.'?pagina='.$menos . $searchUri . '" ><span class="fa fa-chevron-left"></span></a>';
        }

        if (($pagina - 3) < 1) {
            $anterior = 1;
        }else{
            $anterior = $pagina - 3;
        }
        if (($pagina + 3) > $pgs) {
            $posterior = $pgs;
        }else{
            $posterior = $pagina + 3;
        }

        for($i = $anterior; $i <= $posterior; $i++)
        {
            if($i != $pagina) {
                $paginator .= '<a href="'.$post.'?pagina='.$i . $searchUri . '">'.$i.'</a>';
            }else {
                $paginator .= '<a href="javascript:;" class="active">'.$i.'</a>';
            }
        }

        if($mais <= $pgs) {
            $paginator .= '<a href="'.$post.'?pagina='.$mais . $searchUri .'"><span class="fa fa-chevron-right"></a>';
        }

    }

	  return $paginator;
}

function getLots($codigo)
{
    $modelLeilao = new \App\Leiloes;
    return $modelLeilao::with('lots')->find($codigo);
}

function countEnables($codigo)
{
    $modelEnables = new \App\Habilitados;
    return $modelEnables::where('idlote', '=', $codigo)->count();
}

function termoUso()
{
    $termoUso = \App\TermoUso::all();
    $texto = '';
    if ($termoUso->count() > 0) {
        $texto = $termoUso[0]->texto;
    }
    return $texto;
}

function getInstitutionalText($title)
{
    $title = ucwords(str_replace('-', ' ', $title));
    $title = str_replace(' ', '', $title);
    $title = "\\App\\$title";
    $text = $title::all()->toArray();
    return $text[0]['texto'];
}

function formataData($data = '', $time = false)
{
    $data = trim($data);

    if ($data == '' || $data == '0000-00-00') {
        return '';
    }
    if ($time) {
        if (strstr($data, '/')) {
            return \Carbon\Carbon::createFromFormat('d/m/Y H:i:s', $data)->format('Y-m-d H:i:s');
        }
        return \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $data)->format('d/m/Y H:i:s');
    }
    if (strstr($data, '/')) {
        return \Carbon\Carbon::createFromFormat('d/m/Y', $data)->format('Y-m-d');
    }
    return \Carbon\Carbon::createFromFormat('Y-m-d', $data)->format('d/m/Y');
}

function getLocationCache()
{
    $location =  Illuminate\Support\Facades\Cache::remember('localizacao', 3600, function () {
        return \App\Localizacao::all()->first();
    });
    return $location;
}

function getComoParticipar()
{
    $location =  Illuminate\Support\Facades\Cache::remember('comoParticipar', 3600, function () {
        return \App\ComoParticipar::all()->first()->texto;
    });
    return $location;
}

function returnShortLoginName($nome)
{
    $nome = explode(' ', $nome);
    $nome = @$nome[0] . ' ' . @$nome[1];
    return ucwords(strtolower($nome));
}
function statusLeilaoAtividades($codStatus)
{
    $codStatus = (int) $codStatus;

    switch ($codStatus) {
        case 7:
            $str = '<label class="label futuro">FUTURO</label>';
            break;
        case 1:
            $str = '<label class="label aberto">ABERTO</label>';
            break;
        case 8:
            $str = '<label class="label aberto">VENDIDO</label>';
            break;
        case 6:
            $str = '<label class="lable encerrado">Encerrado</label>';
            break;
        case 2:
            $str = '<label class="lable encerrado">Cancelado</label>';
            break;
        case 3:
            $str = '<label class="lable encerrado">Suspenso</label>';
            break;
        case 4:
            $str = '<label class="lable encerrado">Cancelado</label>';
            break;
        case 5:
            $str = '<label class="lable encerrado">Cancelado</label>';
            break;
        case 7:
            $str = '<label class="lable encerrado">Futuro</label>';
            break;
        case 8:
            $str = '<label class="lable encerrado">Vendido</label>';
            break;
        case 9:
            $str = '<label class="lable futuro">Em edição <small>(Sujeito a alterações)</small></label>';
            break;
        case 10:
            $str = '<label class="lable encerrado">Encerrado</label>';
            break;
        case 11:
            $str = '<label class="lable futuro">Retomado pelo banco <small>&nbsp;</small></label>';
            break;
        default:
            $str = '<label class="lable futuro">Futuro</label>';
            break;
    }
    return $str;
}
function encerraEmBreve()
{
    return '<div class="status pulsar"><h5 class="pulsar">Leilão Encerrando <br /> <small>Aproveite a oportunidade!</small></h5></div>';
}
function statusLeilao($lote, $visualizaLote = false, $h = 4, $onlyClass = false)
{
    $status = $lote->statuslote ?? $lote->encerrado;

    if ($lote->tipo == 4 && $status == 1) {
        $status = 12;
    }

    $arrayStatus = [];
    $arrayStatus[1] =  'Aberto<br /><small>Habilite-se agora!</small>|futuro|';
    if ($lote->tipo == 5) {
        $arrayStatus[1] =  'Venda direta<br /><small>Aproveite a oportunidade!</small>|futuro|';
    }
    if ($lote->tipo == 6) {
        $arrayStatus[1] =  'Alienação fiduciária<br /><small>Habilite-se agora!</small>|futuro|';
    }
    $arrayStatus[6] =  'Encerrado<br /><small>Leilão Encerrado</small>|encerrado|Encerrado';
    $arrayStatus[2] =  'Cancelado<br /><small>Leilão cancelado</small>|encerrado|Cancelado';
    $arrayStatus[3] =  'Suspenso<br /><small>Leilão suspenso</small>|encerrado|Suspenso';
    $arrayStatus[4] =  'Cancelado<br /><small>Leilão cancelado</small>|encerrado|Cancelado';
    $arrayStatus[5] =  'Cancelado<br /><small>Leilão cancelado</small>|encerrado|Cancelado';
    $arrayStatus[7] =  'Futuro<br /><small>Habilite-se agora!</small>|aberto|Futuro';
    $arrayStatus[8] =  'Vendido<br /><small>Este leilão se encerrou</small>|aberto|Vendido';
    $arrayStatus[9] =  'Em edição <br /><small>(Sujeito a alterações)</small>|aberto|Em edição <br /><small>(Sujeito a alterações)</small>|Em edição <br /><small>(Sujeito a alterações)</small>';
    $arrayStatus[10] =  'Encerrado<br /><small>Leilão Encerrado</small>|encerrado|Encerrado<br />';
    $arrayStatus[11] =  'Retomado pelo banco<br/ ><small>&nbsp;</small>|aberto|Retomado pelo banco<br/ >|Retomado pelo banco<br/ >';
    $arrayStatus[12] =  'Aberto para Proposta<br/ ><small>Envie sua proposta!</small>|futuro|';
    $status = explode('|', $arrayStatus[$status]);
    $classe = $status[1];
    if ($onlyClass) {
        return $classe;
    }
    $statusLeilao = $status[0];
    if ($visualizaLote) {
        return ['<h' . $h . ' class="' . $classe . '">' . strtoupper($statusLeilao) . '</h' . $h . '>', $status[2]];
    }
    return '<div class="status ' . $classe . '"><h5>' . $statusLeilao . '</h5></div>';
}

function tipoLeilao($tipo)
{
    $tipo = (int) $tipo;
    $strTitpo = 'JUDICIAL';
    switch ($tipo) {
        case 1:
            $strTitpo = 'JUDICIAL';
            break;
        case 2:
            $strTitpo = 'EXTRAJUDICIAL';
            break;
        case 3:
            $strTitpo = 'BENEFICENTE';
            break;
        case 4:
            $strTitpo = 'ALIENAÇÃO PARTICULAR';
            break;
        case 5:
            $strTitpo = 'VENDA DIRETA';
            break;
        default:
            $strTitpo = 'ALIENAÇÃO FIDUCIÁRIA';
            break;
    }
    return $strTitpo;
}

function lelaoDatas($value, $numero = 1, $tipo = 0)
{
    if ($tipo == 3) {
        return '';
    }
    $value = (int) $value;
    switch ($value) {
        case 1:
            return $numero . 'º Leilão';
            break;
        case 2:
            return $numero . 'º Praça';
            break;
        case '3':
            return 'Praça única';
            break;
        case '0':
            return $numero . 'º Praça';
            break;
        default:
            return 'Leilão único';
            break;
    }
}

function leilaoFavoritado($idleilao, $idlote, $idusuario)
{
    $leilaoCurtido = \App\LeiloesCurtidos::where('idusuario', $idusuario)
        ->where('idleilao', $idleilao)
        ->where('idlote', $idlote)
        ->first();
    if ($leilaoCurtido) {
        return true;
    }
    return false;
}

function dadosLote($qtdLote, $leilao, $idLote =  null)
{
    $str = '';
    if ($leilao->encerrado == '11') {
        $str = '<p><br />Avaliação: R$ ' . number_format($leilao->avaliacao, 2, ',', '.') . '</p>';
        if ($leilao->min_venda > 0) {
            $str .= '<p><br />Mínimo de venda: R$ ' . number_format($leilao->min_venda, 2, ',', '.') . '</p>';
        } else {
            $str .= '<p><br />Mínimo de venda: Não informado</p>';
        }
        return [$str, null, urlTitulo($leilao->titulo)];
    }

    $mktimeHoje = time();
    $dataInicial = $leilao->leilao_data_inicial . ' ' . $leilao->leilao_hora_inicial;
    $dataFinal = $leilao->datafinal1 . ' ' . $leilao->horafinal1;
    $dataFinal2 = $leilao->datafinal2 . ' ' . $leilao->horafinal2;
    $timeStampFechamento = strtotime($dataFinal);
    $timeStampFechamento2 = strtotime($dataFinal2);

    $lance_data_1 = ($mktimeHoje <= $timeStampFechamento || $leilao->datafinal1 == '0000-00-00') ? 'R$ ' . number_format($leilao->lance_data_1, 2, ',', '.') : '<s>R$ ' . number_format($leilao->lance_data_1, 2, ',', '.') . '</s>';
    $lance_data_2 = ($mktimeHoje <= $timeStampFechamento2 || $leilao->datafinal2 == '0000-00-00') ? 'R$ ' . number_format($leilao->lance_data_2, 2, ',', '.') : '<s>R$ ' . number_format($leilao->lance_data_2, 2, ',', '.') . '</s>';

    if ($leilao->datafinal1 != '0000-00-00') {
        $str .= '<p>' . lelaoDatas($leilao->leilao2_data_tipo, 1) . ' ' . formataData($leilao->datafinal1) . ' ' . 'às' . ' ' . substr($leilao->horafinal1, 0, -3) . '<br>' . 'LANCE MÍNIMO' . ': ' . $lance_data_1 . '</p>';
    } else {
        $str .= '<p>' . lelaoDatas($leilao->leilao2_data_tipo, 1) . '<br>' . 'LANCE MÍNIMO' . ': ' . $lance_data_1 . '</p>';
    }

    if ($leilao->leilao2_data_tipo != 3 && $leilao->leilao2_data_tipo != 4) {
        if ($leilao->datafinal2 != '0000-00-00') {
            $str .= '<p>' . lelaoDatas($leilao->leilao2_data_tipo, 2) . ' ' . formataData($leilao->datafinal2) . ' ' . 'às' . ' ' . substr($leilao->horafinal2, 0, -3) . '<br>' . 'LANCE MÍNIMO' . ': ' . $lance_data_2 . '</p>';
        } else {
            $str .= '<p>' . lelaoDatas($leilao->leilao2_data_tipo, 2) . '<br>' . 'LANCE MÍNIMO' . ': ' . $lance_data_2 . '</p>';
        }
    } else {
        $str .= '<p><br><br></p>';
    }

    return [$str, null, urlTitulo($leilao->titulo)];
}

function redirectLocal($lote)
{
    $urlTitulo = urlTitulo($lote->titulo);
    $redirect = '/leilao/lote/' . $lote->codigo . '/' . $urlTitulo;
    return $redirect;
}

function lanceMinino($lote)
{
    $time = time();
    $dataInicial1 = strtotime($lote->leilao_data_final . ' ' . $lote->leilao_hora_final);
    $lance1 = $lote->lance_data_1;
    $lance2 = $lote->lance_data_2;
    $lanceMinimo = $lance1;
    if ($dataInicial1 < $time) {
        $lanceMinimo = $lance2;
    }
    if ($lote->getLancesComCadastrosAttribute(false)->count() > 0) {
        $lanceMinimo = $lote->getLancesComCadastrosAttribute(false)[0]->valor + $lote->incremento;
    }
    return $lanceMinimo;
}

function vencedorLeilao($lances, $parcelado =  false, $lanceEscolhido)
{
    $cadastro = \App\Cadastros::find($lances[0]->idcadastro);
    $apelido = $cadastro->apelido;
    $nome = $apelido;
    if ($parcelado) {
        $lanceEscolhido = $lanceEscolhido->toArray();
        if (count($lanceEscolhido) > 0) {
            $cadastro = \App\Cadastros::find($lanceEscolhido[0]['idcadastro']);
            $apelido = $cadastro->apelido;
            $nome = '@' . $apelido;
            return [$nome, formataData($lanceEscolhido[0]['data_lance'], true), $lanceEscolhido[0]['valor']];
        }
        $lances = $lances->toArray();
        $lances = array_values(array_filter($lances, function ($v, $k) {
            return $v['status'] == 0;
        }, ARRAY_FILTER_USE_BOTH));
        if (count($lances) == 0) {
            return false;
        }
        return [$nome, formataData($lances[0]['data_lance'], true), $lances[0]['valor']];
    }
    return [$nome, formataData($lances[0]->data_lance, true), $lances[0]->valor];
}

function urlTitulo($titulo)
{
    $titulo = strtolower($titulo);
    $titulo = str_replace('-', '', $titulo);

    $comAcentos = array('"', '/', '?', '!', ', ', '–', '(', ')', '.', '²', '@', '&', '  ', 'à', 'á', 'â', 'ã', 'ä', 'å', 'ç', 'è', 'é', 'ê', 'ë', 'ì', 'í', 'î', 'ï', 'ñ', 'ò', 'ó', 'ô', 'õ', 'ö', 'ù', 'ü', 'ú', 'ÿ', 'À', 'Á', 'Â', 'Ã', 'Ä', 'Å', 'Ç', 'È', 'É', 'Ê', 'Ë', 'Ì', 'Í', 'Î', 'Ï', 'Ñ', 'Ò', 'Ó', 'Ô', 'Õ', 'Ö', 'O', 'Ù', 'Ü', 'Ú', '-', ',', ':', ';');
    $semAcentos = array('', '_', '', '', ',', '', '-', '-', '', '', 'arroba', 'e', ' ', 'a', 'a', 'a', 'a', 'a', 'a', 'c', 'e', 'e', 'e', 'e', 'i', 'i', 'i', 'i', 'n', 'o', 'o', 'o', 'o', 'o', 'u', 'u', 'u', 'y', 'A', 'A', 'A', 'A', 'A', 'A', 'C', 'E', 'E', 'E', 'E', 'I', 'I', 'I', 'I', 'N', 'O', 'O', 'O', 'O', 'O', '0', 'U', 'U', 'U', '', '-', '-', '-');
    $titulo = str_replace($comAcentos, $semAcentos, $titulo);
    $titulo = str_replace(' ', '-', $titulo);

    return strtolower($titulo);
}


function modalidadeLeilao($modalidade)
{
    $modalidade = (int) $modalidade;
    $desc = '';
    switch ($modalidade) {
        case 1:
            $desc = 'Online';
            break;
        case 2:
            $desc = 'Presencial';
            break;
        default:
            $desc = 'Presencial / Online';
            break;
    }
    return $desc;
}

function aberturaEncerramento($leilao)
{
    $dataInicial = $leilao->leilao_data_inicial . ' ' . $leilao->leilao_hora_inicial;
    if ($leilao->encerrado == 7) {
        return 'Abertura';
    }
    return 'Encerramento';
}

function retornaDataCronometro($leilao, $status)
{
    $time = time();
    $dataInicial1 = strtotime($leilao->leilao_data_inicial . ' ' . $leilao->leilao_hora_inicial);
    $dataFinal1 = strtotime($leilao->leilao_data_final . ' ' . $leilao->leilao_hora_final);

    $dataInicial2 = strtotime($leilao->leilao2_data_inicial . ' ' . $leilao->leilao2_hora_inicial);
    $dataFinal2 = strtotime($leilao->leilao2_data_final . ' ' . $leilao->leilao2_hora_final);
    if ($time <  $dataInicial1) {
        return date('m/d/Y H:i:s', $dataInicial1);
    }

    if ($time >  $dataInicial1 && $time < $dataFinal1) {
        return date('m/d/Y H:i:s', $dataFinal1);
    }

    if ($time <  $dataInicial2) {
        return date('m/d/Y H:i:s', $dataInicial2);
    }

    if ($time >  $dataInicial2 && $time < $dataFinal2) {
        return date('m/d/Y H:i:s', $dataFinal2);
    }

    return date('m/d/Y H:i:s', $dataFinal2);
}

function retornaLanceInicial($lote, $stroke = false, $campo = false)
{
    $time = time();
    $dataFinal = $lote->leilao->leilao_data_final . ' ' . $lote->leilao->leilao_hora_final;
    if ($campo == 'lance_data_2') {
        $dataFinal = $lote->leilao->leilao2_data_final . ' ' . $lote->leilao->leilao2_hora_final;
    }
    $timeStampFechamento = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $dataFinal)->timestamp;

    if ($time >= $timeStampFechamento) {
        if ($campo) {
            $valor = $stroke == true ? '<s>R$ ' . number_format($lote->$campo, 2, ',', '.') . '</s>' : 'R$ ' . number_format($lote->$campo, 2, ',', '.');
            return  $valor;
        }
        return  'R$ ' . number_format($lote->lance_data_1, 2, ',', '.');
    }
    if ($campo) {
        return  'R$ ' . number_format($lote->$campo, 2, ',', '.');
    }
    if ($timeStampFechamento > $time) {
        return  'R$ ' . number_format($lote->lance_data_1, 2, ',', '.');
    }
    return  'R$ ' . number_format($lote->lance_data_2, 2, ',', '.');
}

function redimencionaImagem($imagem, $altura, $largura, $fileName = '')
{
    //REDIMENSIONANDO AS IMAGENS
    $foto = $imagem;

    $tamMax = array($altura, $largura);

    // Comprime imagem
    // 0 => Sem comprimir
    // 100 => Melhor compre��o
    $comprimi = 70;

    //0 => largura
    //1 => Altura
    //2 => Formato da imagem
    list($imgLarg, $imgAlt, $imgTipo) = getimagesize($foto);
    $novaLargura = $imgLarg;
    $novaAltura = $imgAlt;
    //verifica se a imagem � maior que o m�ximo permitido
    if ($imgLarg > $tamMax[0] || $imgAlt > $tamMax[1]) {
        //verifica se a largura � maior que a altura
        if ($imgLarg > $imgAlt) {
            $novaLargura = $tamMax[0];
            $novaAltura = round(($novaLargura / $imgLarg) * $imgAlt);
        }
        //se a altura for maior que a largura
        elseif ($imgAlt > $imgLarg) {
            $novaAltura = $tamMax[1];
            $novaLargura = round(($novaAltura / $imgAlt) * $imgLarg);
        }
        //altura == largura
        else {
            $novaAltura = $novaLargura = max($tamMax);
        }
    }

    // Cria a imagem baseada na imagem original
    switch ($imgTipo) {
        case 1:
            $srcImg = imagecreatefromgif($foto);
            break;

        case 2:
            $srcImg = imagecreatefromjpeg($foto);
            break;

        case 3:
            $srcImg = imagecreatefrompng($foto);
            break;

        default:
            return '';
            break;
    }

    // cria a nova imagem
    $destImg = imagecreatetruecolor(@$novaLargura, @$novaAltura);

    // copia para a imagem de destino a imagem original redimensionada
    imagecopyresampled($destImg, $srcImg, 0, 0, 0, 0, @$novaLargura, @$novaAltura, $imgLarg, $imgAlt);
    // Sava a imagem
    switch ($imgTipo) {
        case 1:
            imagegif($destImg, $imagem, NULL, $comprimi);
            break;
        case 2:
            imagejpeg($destImg, $imagem, $comprimi);
            break;
        case 3:
            imagepng($destImg, $imagem, NULL, $comprimi);
            break;

        default:
            echo '';
            break;
    }

    # send to s3
    $content = File::get($imagem);
    if (Storage::disk('s3')->put($fileName, $content)) {
        unlink($imagem);
    }
}

function retornaIdEstado($estado)
{
    if ($estado == '' || $estado == 'todos-os-estados') {
        return 0;
    }
    $estado = \App\Estados::select('codigo')->where('uf', $estado)->get();
    if ($estado->count() > 0) {
        return (int) $estado[0]->codigo;
    }
    return 88888899;
}

function retornaIdCidade($cidade, $idestado)
{
    if ($cidade == '' || $cidade == 'todas-as-cidades') {
        return 0;
    }
    $cidade = ucwords(str_replace("-", " ", $cidade));
    //die($cidade);
    $cidade = \App\Cidades::select('codigo')->where('nome', $cidade)->where('idestado', $idestado)->get();
    if ($cidade->count() > 0) {
        return (int) $cidade[0]->codigo;
    }
    return 8888889;
}

function retornaIdCategoria($segmento)
{
    $id = 0;
    switch ($segmento) {
        case 'residenciais':
            $id = 4;
            break;
        case 'comerciais':
            $id = 5;
            break;
        case 'rurais':
            $id = 6;
            break;
    }
    return $id;
}

function retornaIdSubCategoria($segmento)
{
    $subcategoria = 0;
    switch ($segmento) {
        case 'todos-os-residenciais':
            $subcategoria = 99;
            break;
        case 'todos-os-comerciais':
            $subcategoria = 100;
            break;
        case 'apartamento':
            $subcategoria = 18;
            break;
        case 'casa-sobrado':
            $subcategoria = 19;
            break;
        case 'sala-escritorios':
            $subcategoria = 20;
            break;
        case 'galpao':
            $subcategoria = 21;
            break;
        case 'terreno-lote-comercial':
            $subcategoria = 22;
            break;
        case 'glebas':
            $subcategoria = 23;
            break;
        case 'terreno-lote':
            $subcategoria = 24;
            break;
        case 'cobertura':
            $subcategoria = 25;
            break;

        case 'fazenda':
            $subcategoria = 26;
            break;
        case 'chacara-sitio':
            $subcategoria = 27;
            break;
        case 'veiculos':
            $subcategoria = 28;
            break;
        case 'veiculos':
            $subcategoria = 29;
            break;
        case 'vaga-de-garagem-residencial':
            $subcategoria = 30;
            break;
        case 'vaga-de-garagem-comercial':
            $subcategoria = 31;
            break;
    }
    return $subcategoria;
}

function getAdmEmails()
{
    // $emails = \App\User::where('tipo', 1)->get()->pluck('email')->toArray();
    return [
        '<EMAIL>'
    ];
}

function getAllEmails()
{
    $emails = \App\User::get()->pluck('email')->toArray();
    return $emails;
}

function leiloesDestaque()
{
    $location =  Illuminate\Support\Facades\Cache::remember('localizacao', 3600, function () {
        return \App\Localizacao::all()->first();
    });
    Illuminate\Support\Facades\Cache::forget('destaques');
    $destaques = Illuminate\Support\Facades\Cache::remember('destaques', 300, function () {
        return \Illuminate\Support\Facades\DB::select('select * from (
            select * from (SELECT DISTINCT A.*,  B.categoria, B.codigo as codigolote, B.encerrado as statuslote, B.idleilao,  B.leilao_data_final as datafinal1, B.leilao_hora_final as horafinal1, B.leilao2_data_final as datafinal2, B.leilao2_hora_final as horafinal2, B.destaque as destaque_lote,
            B.lance_data_1, B.lance_data_2,
            (SELECT  IF(now() < Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime), 1, 2) as praca) as praca
            FROM leiloes A
            INNER JOIN lotes B ON A.codigo = B.idleilao
            WHERE A.encerrado = 1 and A.suspender = 2
            AND
            B.lote_destaque = 1 and A.deleted_at is null and B.deleted_at is null
            order by praca asc, A.leilao_data_final asc,  A.leilao_hora_final asc, A.leilao2_data_final asc,  A.leilao2_hora_final asc
            ) as aberto

            union all

            select * from (SELECT DISTINCT A.*,  B.categoria, B.codigo as codigolote, B.encerrado as statuslote, B.idleilao,  B.leilao_data_final as datafinal1, B.leilao_hora_final as horafinal1, B.leilao2_data_final as datafinal2, B.leilao2_hora_final as horafinal2, B.destaque as destaque_lote,
            B.lance_data_1, B.lance_data_2,
            (SELECT  IF(now() < Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime), 1, 2) as praca) as praca
            FROM leiloes A
            INNER JOIN lotes B ON A.codigo = B.idleilao
            WHERE A.encerrado in(7,9) and A.suspender = 2
            AND
            B.lote_destaque = 1 and A.deleted_at is null and B.deleted_at is null
            order by praca asc, A.leilao_data_final asc,  A.leilao_hora_final asc, A.leilao2_data_final asc,  A.leilao2_hora_final asc
            ) as futuro
) as todos
limit 8');
    });
    return $destaques;
}

function maiorLanceParcelado($lances)
{
    if ($lances->count() > 0) {
        $lances = $lances->toArray();
        $lancesParc0 = array_filter($lances, function ($k, $v) {
            return $k['parcelas'] == 0;
        }, ARRAY_FILTER_USE_BOTH);
        if (count($lancesParc0) > 0) {
            return [$lancesParc0[0]['valor'], $lancesParc0[0]['data_lance']];
        }
        return [$lances[0]['valor'], $lances[0]['data_lance']];
    }
    return false;
}

function ultimosLancesOfertados($lote)
{
    $lancesMerge = $lote->getLancesComCadastrosAttribute();
    $arrayLances = array();
    foreach ($lancesMerge as $dataLance) {
        $timeLance = strtotime($dataLance['data_lance'])  + $dataLance['codigo'];
        $arrayLances[$timeLance] = $dataLance;
    }
    $html = '';
    $qtdView = 5;
    if (count($lancesMerge) > 0) {
        $html = '';
        foreach ($arrayLances as $dataLance) {
            $automatico = '&nbsp;';
            $apelido = strtolower($dataLance['apelido']);
            $apelido = str_replace(' ', '', $apelido);
            $tipoLance = @$dataLance['tipo'] == 1 ? 'Proposta parcelada' : 'Lance';
            if ($dataLance['automatico'] == '1') {
                $tipoLance = "Lance Automático";
            }
            if (strlen($apelido) > 10) {
                $apelido = substr($apelido, 0, 10) . '...';
            }
            $html .= '<tr class="tr-head-lance">';
            $html .= '<td>' . $tipoLance . '</td>';
            $html .= '<td>@' . $apelido . '</td>';
            $html .= '<td>R$ ' . number_format($dataLance['valor'], 2, ',', '.') . '</td>';
            $html .= '<td>' . formataData($dataLance['data_lance'], true) . '</td>';
            $html .= '</tr>';

            $qtdView--;
            if ($qtdView == 0) {
                break;
            }
        }
        return $html;
    }
    return '<tr class="tr-head-lance"><td colspan="4" class="nenhum" >Nenhum lance vencendo até o momento</td></tr>';
}

function maiorLance($lote)
{
    $html = '';
    if ($lote->getLancesComCadastrosAttribute()->count() > 0) {
        $html .= '<div class="row titulo">
            <div class="col-xs-6">
                <p>Valor</p>
            </div>
            <div class="col-xs-6">
                <p>Data</p>
            </div>
        </div>';
        $html .= '<div class="row">
            <div class="col-xs-6">
                <p>R$ ' . number_format($lote->getLancesComCadastrosAttribute()[0]->valor, 2, ',', '.') . '</p>
            </div>
            <div class="col-xs-6">
                <p>' . formataData($lote->getLancesComCadastrosAttribute()[0]->data_lance, true) . '</p>
            </div>
        </div>';
        $html .=  statusLeilao($lote, true, 5)[0];
    } else if ($lote->maiorLanceParcelado->count() > 0) {
        $maiorLance = maiorLanceParcelado($lote->maiorLanceParcelado);
        $html .= '<div class="row titulo">
            <div class="col-xs-6">
                <p>Valor</p>
            </div>
            <div class="col-xs-6">
                <p>Data</p>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-6">
                <p>R$ ' . number_format($maiorLance[0], 2, ',', '.') . '</p>
            </div>
            <div class="col-xs-6">
                <p>' . formataData($maiorLance[1], true) . '</p>
            </div>
        </div>';
        $html .=  statusLeilao($lote, true, 5)[0];
    } else {
        $html .= '<h5 class="nenhum">Nenhum lance vencendo até o momento</h5>';
        $html .=  statusLeilao($lote, true, 5)[0];
    }
    return $html;
}
function ConverteData()
{
    return true;
}

function retornaSubCategoria($id)
{
    $subcategoria = [];
    $subcategoria[18] = 'Apartamento';
    $subcategoria[25] = 'Cobertura';
    $subcategoria[19] = 'Casa';
    $subcategoria[24] = 'Terreno';
    $subcategoria[30] = 'Vaga';
    $subcategoria[20] = 'Salas';
    $subcategoria[21] = 'Galpão';
    $subcategoria[22] = 'Terreno';
    $subcategoria[31] = 'Vaga';
    $subcategoria[23] = 'Glebas';
    $subcategoria[26] = 'Fazenda';
    $subcategoria[27] = 'Chácara';
    $subcategoria[1] = 'Apartamento';
    $subcategoria[2] = 'Barco';
    $subcategoria[3] = 'Caminhão';
    $subcategoria[4] = 'Casa';
    $subcategoria[5] = 'Chevrolet';
    $subcategoria[6] = 'Conjunto';
    $subcategoria[7] = 'Eletrônicos';
    $subcategoria[8] = 'GALPÃO';
    $subcategoria[9] = 'Informática';
    $subcategoria[10] = 'Metade';
    $subcategoria[11] = 'Moto';
    $subcategoria[12] = 'Plataforma';
    $subcategoria[13] = 'Sinistrados';
    $subcategoria[14] = 'Sucatas';
    $subcategoria[15] = 'Terreno';
    $subcategoria[16] = 'Vaga';
    $subcategoria[17] = 'Carros';
    $subcategoria[28] = 'Veículos';
    $subcategoria[29] = 'Diversos';
    if (isset($subcategoria[$id])) {
        return $subcategoria[$id];
    }
    return 'N/A';
}

function retornaSituacao($id)
{
    $id = (int) $id;
    if ($id == 1) {
        return 'Ocupado';
    }
    if ($id == 2) {
        return 'Desocupado';
    }
    return 'Não informado';
}

function retornaValorDesconto($lote, $valor = null)
{
    if (!isset($lote->lance_data_2, $lote->avaliacao, $lote->encerrado)) {
        return false;
    }

    if ($lote->lance_data_2 >= $lote->avaliacao && $lote->encerrado !== '11') {
        return false;
    }

    $avaliacao = (float) $lote->avaliacao;
    if ($avaliacao < 0.01) {
        return 0;
    }

    $lanceData2 = isset($valor) && $valor > 0 ? (float) $valor : (float) $lote->lance_data_2;
    $desconto = max(0, min(100, 100 - (($lanceData2 * 100) / $avaliacao)));

    return (int) round($desconto);
}

function classCssPracaAtual($lote)
{
    $time = time();
    $timeStamp = strtotime($lote->leilao_data_final . ' ' . $lote->leilao_hora_final);
    if ($time > $timeStamp) {
        return ['leilao-gray', 'leilao-green', 2];
    }
    return ['leilao-green', 'leilao-gray', 1];
}

function covertToMegabyte($size, $precision = 2)
{
    $size = (int) $size;
    if ($size > 0) {
        return round($size / 1000000, $precision);
    }
    return $size;
}

function mostraDivBairroeMetroQuadrado($loteId = 0)
{
    $cidade = trim(strtolower(Request::segment(5)));
    if ($cidade == '') {
        return [null, null];
    }

    if ($cidade != 'taquarituba') {
        return [null, null];
    }
    $lote = \App\Lotes::find($loteId);
    $titulo = $lote->titulo;
    if (strlen($titulo) > 47) {
        $titulo = substr($titulo, 0, 47) . '...';
    }
    return ['<div class="div-detalhe-card">' . $titulo . '</div>', '<div class="div-detalhe-card div-detalhe-card-metragem""><i class="fa fa-cube fa-3x" aria-hidden="true"></i><br />' . $lote->metragem . 'm²</div><br />'];
}

function mostraTituloLanding($qtdLeiloes = 0)
{
    $cidade = trim(Request::segment(5));
    if ($cidade == '' || $qtdLeiloes === 0) {
        return;
    }
    $cidade = strtolower($cidade);
    if ($cidade != 'taquarituba') {
        return '';
    }

    $html = '<div clas="row">
            <div class="col-sm-12 padding-10">
                <div class="div-detalhe-card div-detalhe-card-titulo-landing">IMÓVEIS EM ' . $cidade . '</div>
            </div><br /><br />
        </div>';
    return $html;
}

function removeSpecialChars($string)
{
    $string = str_replace(' ', '-', $string);
    return preg_replace('/[^A-Za-z0-9\-]/', '', $string);
}

function getFuso($num_processo)
{
    if (preg_match('/\.(\d)\.(\d{2})\./', $num_processo, $matches)) {
        $justica = $matches[1];
        $codigo_tribunal = $matches[2];

        $mapa_validos = [
            '8' => ['11'], // Justiça Estadual - TJMT
            '4' => ['01'], // Justiça Federal - TRF1
            '5' => ['23'], // Justiça do Trabalho - TRT23
        ];

        if (isset($mapa_validos[$justica]) && in_array($codigo_tribunal, $mapa_validos[$justica])) {
            return 'America/Cuiaba';
        }
    }

    return 'America/Sao_Paulo';
}
