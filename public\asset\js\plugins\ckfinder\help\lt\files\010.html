<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>CK<PERSON>inder vartotojo instrukcija</title>
	<link href="../../files/other/help.css" type="text/css" rel="stylesheet" />
	<script type="text/javascript" src="../../files/other/help.js"></script>
	<meta name="robots" content="noindex, nofollow" />
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" /></head>
<body>
	<h1>
		Statuso būsena
	</h1>
	<p>
		"Statuso būsena" tai yra nedidelė vieta nar<PERSON>, kuri yra naudojama informacijai
        atvaizduoti. Tai gali būti failo apim<PERSON>, jų kiekis segtuve ir t.t Ją galima pamatyti CKFinder
		vartotojo sąsajos apačioje.
	</p>
	<p>
		Jeigu <strong>failas yra pasirinktas</strong> CKFinder'yje, statuso būsena parodys
		detalią informaciją apie failą, įskaitant failo pavadinimą, apimtį ir paskutinio atnaujinimo
		datą. Pvz:</p>
	<p style="text-align: center">
		<img src="images/018.gif" height="17" width="221" />&nbsp;</p>
	<p>
		Jeigu <strong>nepasirinktas joks failas</strong>, bus rodomas visų failų kiekis
		esančiame segtuve. Pavyzdžiui:</p>
	<p style="text-align: center">
		<img src="images/019.gif" height="17" width="221" />&nbsp;</p>
</body>
</html>
