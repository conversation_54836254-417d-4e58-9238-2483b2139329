<!doctype html>

<title>CodeMirror: Turtle mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="turtle.js"></script>
<style>.CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}</style>
<div id=nav>
  <a href="http://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png"></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">Turtle</a>
  </ul>
</div>

<article>
<h2>Turtle mode</h2>
<form><textarea id="code" name="code">
@prefix foaf: <http://xmlns.com/foaf/0.1/> .
@prefix geo: <http://www.w3.org/2003/01/geo/wgs84_pos#> .
@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .

<http://purl.org/net/bsletten> 
    a foaf:Person;
    foaf:interest <http://www.w3.org/2000/01/sw/>;
    foaf:based_near [
        geo:lat "34.0736111" ;
        geo:lon "-118.3994444"
   ]

</textarea></form>
    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        mode: "text/turtle",
        matchBrackets: true
      });
    </script>

    <p><strong>MIME types defined:</strong> <code>text/turtle</code>.</p>

  </article>
