<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>CKFinder User's Guide</title>
	<link href="../../files/other/help.css" type="text/css" rel="stylesheet" />
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<script type="text/javascript" src="../../files/other/help.js"></script>
	<meta name="robots" content="noindex, nofollow" />
</head>
<body>
	<h1>
		Bot&oacute;n Ajustes </h1>
	<p>
		El bot&oacute;n "Ajustes", en <a href="005.html">la barra de herramientas</a>, abre el &quot;p&aacute;nel de ajustes&quot;, donde usted podr&aacute; configurar y personalizar CK<PERSON>inder, he aqu&iacute; un screenshot de el:</p>
	<p style="text-align: center">
		<img src="images/013.png" height="189" width="416" alt="Panel de Configuración"/></p>
	<p>
		Todos los ajustes son guardados automaticamente utilizando las denominadas &quot;Cookies&quot; de su browser. Las &quot;Cookies&quot; son peque&ntilde;os archivos que contienen informaci&oacute;n de configuraci&oacute;n proivada para sitios web especificos.</p>
	<p>
		Para cerrar el p&aacute;nel de ajustes, solo d&eacute; click sobre el bot&oacute;n &quot;Cerrar&quot; o d&eacute; click nuevamente sobre el bot&oacute;n &quot;Ajustes&quot; de la barra de herramientas.</p>
	<h2>
		Opciones de Configuraci&oacute;n </h2>
	<p>
		Todas las opciones de configuraci&oacute;n tienen que ver con el <a href="004.html">P&aacute;nel de Archivos </a>.
		Se usan para controlar como desplegar informaci&oacute;n en ese p&aacute;nel. El P&aacute;nel de Archivos, reacciona inmediatamente a los cambios hechos en el P&aacute;nel de ajustes.</p>
	<h3>
		Vista</h3>
	<p>
		Controla el modo actual de vista del <a href="004.html">P&aacute;nel de Archivos</a>:</p>
	<ul>
		<li>El modo "<strong>Miniaturas</strong>" desplegar&aacute; cada archivo como una &quot;caja&quot;. Para las imagenes, una peque&ntilde;a previsualizaci&oacute;n de ella (llamada miniatura) ser&aacute; desplegada dentro de la caja. Para otros tipos de archivo, un icono ser&aacute; mostrado en su lugar.</li>
	</ul>
	<ul>
		<li>El modo "<strong>Lista</strong>" desplegar&aacute; todos los archivos como una lista, uno abajo del otro. No se mostrar&aacute; ning&uacute;n tipo de previsualizaci&oacute;n en este modo.</li>
	</ul>
	<h3>
		Mostrar</h3>
	<p>
		Ajusta la cantidad de informaci&oacute;n disponible en el p&aacute;nel de archivos. Para ejemplificar, he aqu&iacute; como se mostrar&iacute;an los archivos cuando ning&uacute;na opci&oacute;n est&aacute; seleccionada, hasta llegar a tener todas las opciones seleccionadas.</p>
	<table align="center" cellpadding="0" cellspacing="0">
			<tr>
				<td valign="top" style="padding-right: 10px">
					<img src="images/014.gif" width="112" height="112" /></td>
				<td valign="top" style="padding-right: 10px; padding-left: 10px">
					<img src="images/015.gif" width="112" height="128" /></td>
				<td valign="top" style="padding-right: 10px; padding-left: 10px">
					<img src="images/016.gif" width="112" height="144" /></td>
				<td valign="top" style="padding-left: 10px">
					<img src="images/017.gif" width="112" height="160" /></td>
			</tr>
</table>
	</p>
	<h3>
		Ordenamiento</h3>
	<p>
		Controla el orden en el que los archivos ser&aacute;n listados. Puede ser alfabeticamente por el nombre del archivo, por fecha de creaci&oacute;n del archivo siendo el  m&aacute;s nuevo el primero o incluso por el tama&ntilde;o del archivo.</p>
</body>
</html>
