<?php

namespace App;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;

class Lotes extends Model implements Auditable
{
    use Notifiable;
    use SoftDeletes;
    use \OwenIt\Auditing\Auditable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'idleilao',
        'categoria',
        'subcategoria',
        'idestado',
        'idcidade',
        'bairro',
        'endereco',
        'numero',
        'titulo',
        'subtitulo',
        'avaliacao',
        'min_venda',
        'debitos',
        'lance_data_1',
        'lance_data_2',
        'incremento',
        'abertura',
        'fechamento',
        'num_processo',
        'url_consulta',
        'vara',
        'juiz',
        'comissao_leiloeiro',
        'nome_exequente',
        'doc_exequente',
        'nome_executado',
        'doc_executado',
        'nome_depositario',
        'cpf_depositario',
        'rg_depositario',
        'edital',
        'publicacao',
        'doe',
        'cda',
        'chb',
        'descricao',
        'visitacao',
        'compra_parcelada',
        'mapa',
        'lote_destaque',
        'requer_habilitacao',
        'exibir_valor',
        'encerrado',
        'restrito',
        'suspender',
        'ind_parcelada',
        'ind_envio',
        'ind_status',
        'leilao_data_inicial',
        'leilao_hora_inicial',
        'leilao_data_final',
        'leilao_hora_final',
        'leilao2_data_inicial',
        'leilao2_hora_inicial',
        'leilao2_data_final',
        'leilao2_hora_final',
        'metragem',
        'dormitorios',
        'garagem',
        'banheiros',
        'situacao',
        'desc_condicao_pagamento',
        'numero_lote'
    ];

    protected $table = 'lotes';
    protected $primaryKey = 'codigo';

    public function __construct()
    {
        parent::__construct();

        $middlewares = (\Route::current());

        if ($middlewares) {
            $middlewares = $middlewares->gatherMiddleware();
            if (in_array('auth', $middlewares) == true) {
                if (session()->has('portaldb')) {
                    $this->table = session()->get('portaldb') . '.' . $this->table;
                }
            }
        }
    }

    public function habilitados()
    {
        return $this->hasManyThrough('App\Cadastros', 'App\Habilitados', 'idlote', 'codigo', 'codigo', 'idcadastro');
    }

    public function listaHabilitados()
    {
        return $this->hasMany('App\Habilitados', 'idlote', 'codigo');
    }

    public function leilao()
    {
        return $this->hasOne('App\Leiloes', 'codigo', 'idleilao');
    }
    public function estado()
    {
        return $this->hasOne('App\Estados', 'codigo', 'idestado');
    }

    public function cidade()
    {
        return $this->hasOne('App\Cidades', 'codigo', 'idcidade');
    }

    public static function estadoCidadesBairrosDisponiveis($idestado = 0)
    {
        $idestado = (int) $idestado;
        $lotes = self::join('cidades', 'cidades.codigo', 'lotes.idcidade')
            ->join('estados', 'estados.codigo', 'lotes.idestado')
            ->join('leiloes', 'lotes.idleilao', '=', 'leiloes.codigo')
            ->select('lotes.idestado', 'lotes.idcidade', 'cidades.nome as cidade', 'estados.nome as uf', 'bairro')
            ->whereIn('leiloes.encerrado', [1, 7, 9, 11])
            ->whereIn('lotes.encerrado', [1, 7, 9, 11])
            ->where('lotes.idestado', $idestado)
            ->groupBy('lotes.idestado', 'lotes.idcidade', 'bairro')
            ->orderBy('lotes.idestado')
            ->orderBy('lotes.idcidade')
            ->orderBy('bairro')
            ->get();

        return ['localizacoes' => $lotes];
    }

    public static function estadosDisponiveis($idestado = 0)
    {
        $idestado = (int) $idestado;
        $lotes = self::join('estados', 'estados.codigo', 'lotes.idestado')
            ->join('leiloes', 'lotes.idleilao', '=', 'leiloes.codigo')
            ->select('lotes.idestado', 'estados.uf', 'estados.nome')
            ->whereIn('leiloes.encerrado', [1, 7, 9, 11])
            ->whereIn('lotes.encerrado', [1, 7, 9, 11])
            ->where('leiloes.suspender', 2)
            ->groupBy('lotes.idestado')
            ->orderBy('estados.nome')
            ->get();

        return ['estados' => $lotes];
    }

    public static function cidadesDisponiveis($idestado = 0)
    {
        $idestado = (int) $idestado;

        $lotes = self::join('cidades', 'cidades.codigo', 'lotes.idcidade')
            ->join('leiloes', 'lotes.idleilao', '=', 'leiloes.codigo')
            ->select('lotes.idcidade', 'cidades.nome')
            ->whereIn('leiloes.encerrado', [1, 7, 9, 11])
            ->whereIn('lotes.encerrado', [1, 7, 9, 11])
            ->where('lotes.idestado', $idestado)
            ->where('leiloes.suspender', 2)
            ->groupBy('lotes.idcidade')
            ->orderBy('cidades.nome')
            ->get();

        return ['cidades' => $lotes];
    }

    public static function bairrosDisponiveis($idcidade, $idestado)
    {
        $bairros = self::join('leiloes', 'lotes.idleilao', '=', 'leiloes.codigo')
            ->select('bairro')->distinct()
            ->whereIn('leiloes.encerrado', [1, 7, 9, 11])
            ->whereIn('lotes.encerrado', [1, 7, 9, 11])
            ->where('leiloes.suspender', 2)
            ->where('idestado', $idestado);
        if ($idcidade > 0) {
            $bairros = $bairros->where('idcidade', $idcidade);
        }

        return ['bairros' => $bairros->get()];
    }

    public function imagensLote()
    {
        return $this->hasMany('App\LotesImagens', 'idlote', 'codigo')->orderBy('ordem');
    }

    public function maiorLance()
    {
        return $this->hasMany('App\Lances', 'idlote', 'codigo')
            ->join('cadastros', 'cadastros.codigo', '=', 'lances.idcadastro')
            ->where('lances.desativado', '!=', 1)
            ->select('lances.*', 'cadastros.email', 'cadastros.apelido')
            ->orderBy('valor', 'desc');
    }

    public function loteFavorito()
    {
        return $this->hasOne('App\LeiloesCurtidos', 'idlote', 'codigo');
    }

    public function lances()
    {
        return $this->hasMany('App\Lances', 'idlote', 'codigo')
            ->where('desativado', '!=', 1)
            ->orderBy('valor', 'desc');
    }

    public function getLancesComCadastrosAttribute($all = true)
    {
        $lances = $this->lances;
        if ($all) {
            $lancesParcelados = $this->lancesParcelados;
            $lances = $lances->merge($lancesParcelados);
        }
        $idsCadastros = $lances->pluck('idcadastro')->unique();

        $cadastros = DB::connection('mysql_2')->table('cadastros')
            ->whereIn('codigo', $idsCadastros)
            ->get()
            ->keyBy('codigo');

        return $lances->map(function ($lance) use ($cadastros) {
            $lance->email = $cadastros[$lance->idcadastro]->email ?? null;
            $lance->apelido = $cadastros[$lance->idcadastro]->apelido ?? null;
            return $lance;
        })->sortByDesc('valor')->values();
    }

    public function tituloOuSubtitulo()
    {
        if (env('SHOW_SUBTITLE_ON_LOTE_VIEW') == true) {
            return $this->subtitulo;
        }
        return $this->titulo;
    }

    public function lancesParcelados()
    {
        return $this->hasMany('App\LancesParcelados', 'idlote', 'codigo')->where('desativado', '!=', '1');
    }

    public function maiorLanceParcelado()
    {
        return $this->hasMany('App\LancesParcelados', 'idlote', 'codigo')
            ->join('cadastros', 'cadastros.codigo', '=', 'lances_parcelados.idcadastro')
            ->join('compras_parceladas', 'compras_parceladas.idlance', '=', 'lances_parcelados.codigo')
            ->where('lances_parcelados.desativado', '!=', 1)
            ->select('lances_parcelados.*', 'cadastros.email', 'parcelas', 'cadastros.apelido')
            ->orderBy('valor', 'desc')
            ->orderBy('parcelas', 'asc');
    }

    public function lanceParceladoEscolhido()
    {
        return $this->hasMany('App\LancesParcelados', 'idlote', 'codigo')
            ->join('cadastros', 'cadastros.codigo', '=', 'lances_parcelados.idcadastro')
            ->join('compras_parceladas', 'compras_parceladas.idlance', '=', 'lances_parcelados.codigo')
            ->where('lances_parcelados.desativado', '!=', 1)
            ->where('lances_parcelados.status', '=', 1)
            ->select('lances_parcelados.*', 'cadastros.email', 'parcelas')
            ->orderBy('valor', 'desc')
            ->orderBy('parcelas', 'asc');
    }

    public function anexosLote()
    {
        return $this->hasMany('App\LotesAnexos', 'idlote', 'codigo')->orderBy('codigo');
    }

    public function lancesLote()
    {
        return $this->hasMany('App\Lances', 'idlote', 'codigo');
    }

    public function habilitadosComLances()
    {
        return $this->hasManyThrough('App\Cadastros', 'App\Lances', 'idlote', 'codigo', 'codigo', 'idcadastro')
            ->groupBy('cadastros.codigo');
    }

    protected static function boot()
    {
        parent::boot();
        static::updated(function ($lote) {
            $encerraLeilao = true;
            if ($lote->leilao->lots()->whereIn('encerrado', [1, 7, 9, 11])->count() > 0) {
                return false;
            }

            $leilao = $lote->leilao;

            if ($leilao) {
                $leilao->encerrado = 6;
                $leilao->save();
            }
            return true;
        });
    }
}
