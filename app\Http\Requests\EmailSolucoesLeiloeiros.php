<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class EmailSolucoesLeiloeiros extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'nome' => 'required',
            'leiloleiro' => 'required',
            'email' => 'required|email:rfc',
            'whatsapp' => 'required'
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'nome.required' => 'Informe seu nome',
            'leiloleiro.required'  => 'Informe se você é leiloeiro ou não',
            'email.required'  => 'Informe seu e-mail',
            'whatsapp.required'  => 'Informe seu Whatsapp',
        ];
    }
}
