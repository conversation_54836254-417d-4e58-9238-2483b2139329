<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddColumnDelayEncerramentoLotes extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('leiloes', function (Blueprint $table) {
            //
            $table->integer('delay_encerramento_lotes')->nullable(true);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('leiloes', function (Blueprint $table) {
            //
            $table->dropColumn('delay_encerramento_lotes');
        });
    }
}
