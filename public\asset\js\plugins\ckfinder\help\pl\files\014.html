<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>CKFinder &mdash; Podręcznik Użytkownika</title>
	<link href="../../files/other/help.css" type="text/css" rel="stylesheet" />
	<script type="text/javascript" src="../../files/other/help.js"></script>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta name="robots" content="noindex, nofollow" />
</head>
<body>
	<h1>
		<PERSON><PERSON><PERSON></h1>
	<p>
		<strong><PERSON><PERSON><PERSON></strong> to folder dostępny w
		<strong><a href="003.html">panelu folderów</a></strong>, otwierający
		<strong>panel koszyka</strong>.</p>
	<p>
		<strong><PERSON><PERSON><PERSON></strong> jest wirtualnym, tymczasowym pojemnikiem wykorzystywanym
		do wykonywania operacji na grupach plików w CKFinderze. Jest pojemnikiem
		<em>wirtualnym</em>, gdyż pliki umieszczane w <strong>koszyku</strong> nie są
		fizycznie przenoszone z ich folderów macierzystych.</p>
	<p>Poniższy rysunek pokazuje <strong>panel koszyka</strong> CKFindera, który
		zostaje rozwinięty po kliknięciu folderu <strong>Koszyk (Basket)</strong> w
		<strong>panelu folderów</strong>.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_basket_empty.png" width="548" height="235" alt="Folder Koszyka w CKFinderze" />&nbsp;</p>
	<p>
		<span class="info">Uwaga:</span> <strong>Koszyk</strong> jest folderem prywatnym, co
		oznacza, iż nie jest współdzielony z innymi użytkownikami systemu plików i powiązany
		jest z Twoją sesją przeglądarki internetowej.</p>
	<h2>
		Dodawanie plików do koszyka</h2>
	<p>
		Na początku każdej sesji CKFindera <strong>koszyk</strong> jest pusty i
		wyświetlony zostaje komunikat zachęcający do przeciągnięcia do niego plików.
		W celu dodania plików do koszyka przejdź do folderu zawierającego plik, który
		zamierzasz użyć, przeciągnij go nad folder <strong>koszyka</strong>
		w <strong>panelu folderów</strong>, i upuść.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_basket_drag.png" width="482" height="303" alt="Przeciąganie plików do Koszyka w CKFinderze" />&nbsp;</p>
	<p>Plik powinien się teraz pojawić w folderze <strong>koszyka</strong>.</p>
	<h2>
		Menu kontekstowe pliku</h2>
	<p>
		Kiedy plik zostaje umieszczony w <strong>koszyku</strong>, jego menu kontekstowe zmieni się
		w taki sposób, by uwzględniać jedynie operacje dostępne w tym specjalnym folderze.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_basket_file_menu.png" width="137" height="130" alt="Menu kontekstowe pliku w Koszyku" />&nbsp;</p>
	<h2>
		Usuwanie plików z koszyka</h2>
	<p>
		Istnieją dwie metody usuwania pliku z <strong>koszyka</strong>. Po pierwsze, można
		usunąć pojedynczy plik, wybierając opcję <strong>Usuń z koszyka (Remove from Basket)</strong>
		z menu kontekstowego pliku. Po wybraniu tej opcji wyświetlone zostanie okno dialogowe
		z potwierdzeniem.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_basket_remove.png" width="342" height="149" alt="Usunięcie pliku z Koszyka w CKFinderze" />&nbsp;</p>
	<p>Możliwe jest również usunięcie wszystkich plików z koszyka naraz poprzez kliknięcie
		przycisku <strong>Wyczyść koszyk (Clear Basket)</strong> na pasku narzędzi. Po kliknięciu przycisku
		zostaniesz poproszony o potwierdzenie, czy chcesz wyczyścić całą zawartość <strong>koszyka</strong>.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_basket_clear.png" width="304" height="149" alt="Usunięcie wszystkich plików z Koszyka w CKFinderze" />&nbsp;</p>
	<p>
		<span class="info">Uwaga:</span> Usunięcie pliku z <strong>koszyka</strong> nie usuwa go
		z systemu plików. Plik ten w dalszym ciągu będzie dostępny w swoim folderze macierzystym.</p>
	<h2>
		Otwarcie folderu zawierającego plik</h2>
	<p>
		Jak wspomniano wyżej, <strong>koszyk</strong> jest folderem wirtualnym, który pokazuje pliki
		fizycznie znajdujące się w innych folderach systemu plików. Jeśli chcesz odnaleźć folder
		źródłowy pliku, wybierz opcję <strong>Otwórz folder z plikiem (Open Parent Folder)</strong>
		z menu kontekstowego pliku. CKFinder otworzy wtedy folder, w którym oryginalnie znajduje się
		plik.</p>
	<h2>
		Kopiowanie plików z koszyka</h2>
	<p>
		Po umieszczeniu plików w <strong>koszyku</strong> będziesz mógł skopiować je do innego (fizycznego)
		folderu. W celu skopiowania plików wskaż folder docelowy w <strong><a href="003.html">panelu folderów</a>
		</strong> i wybierz opcję <strong>Skopiuj pliki z koszyka (Copy Files from Basket)</strong>
		z menu kontekstowego folderu.</p>
	<p>
		Wyświetlone zostanie okno potwierdzenia wymieniające pliki, które zostały skopiowane do
		folderu docelowego.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_folder_copied_from_basket.png" width="304" height="206" alt="Kopiowanie plików z Koszyka w CKFinderze" />&nbsp;</p>
	<p>
		Pliki te zostaną zduplikowane, a ich kopie umieszczone będą w folderze docelowym. Zawartość
		folderu źródłowego nie zmieni się.</p>
	<h2>Przenoszenie plików z koszyka</h2>
	<p>
		<strong>Koszyk</strong> przydaje się również wtedy, gdy chcesz przenieść jakieś pliki pomiędzy
		folderami. Po umieszczeniu plików w <strong>koszyku</strong> należy wskazać folder docelowy w
		<strong><a href="003.html">panelu folderów</a></strong> i wybrać opcję <strong>Przenieś pliki
		z koszyka (Move Files from Basket)</strong> z menu kontekstowego folderu.</p>
	<p>
		Wyświetlone zostanie okno potwierdzenia wymieniające pliki, które zostały przeniesione do
		folderu docelowego.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_folder_moved_from_basket.png" width="304" height="206" alt="Przesuwanie plików z Koszyka w CKFinderze" />&nbsp;</p>
	<p>
		Pliki te zostaną usunięte z folderu źródłowego i dodane do folderu docelowego.</p>
</body>
</html>
