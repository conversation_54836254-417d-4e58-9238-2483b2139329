<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;
use Auth;

class CadastroSite extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'nome' => 'required_unless:pessoa,"2"',
            'cpf' => 'required_unless:pessoa,"2"|unique:mysql_2.cadastros,cpf,' . (\Auth::user()->codigo  ?? 0).',codigo',
            'rg' => 'required_unless:pessoa,"2"',
            'conjuge' => 'required_if:estado_civil,"2"',
            'c_cpf' => 'required_if:estado_civil,"2"',
            'c_rg' => 'required_if:estado_civil,"2"',
            'profissao' => 'required_unless:pessoa,"2"',
            'razao_social' => 'required_unless:pessoa,"1"',
            'cnpj' => 'required_unless:pessoa,"1"|unique:mysql_2.cadastros,cnpj,' . (\Auth::user()->codigo  ?? 0).',codigo',
            'responsavel_cadastro' => 'required_unless:pessoa,"1"',
            'socio' => 'required_unless:pessoa,"1"',
            's_cpf' => 'required_unless:pessoa,"1"',
            's_rg' => 'required_unless:pessoa,"1"',
            'filiacao' => 'required_unless:pessoa,"2"',
            'data_nascimento' => 'date_format:"d/m/Y"|required_unless:pessoa,"2"',
            'email' => 'required|email:rfc|unique:mysql_2.cadastros,email,' . (\Auth::user()->codigo  ?? 0).',codigo',
            'apelido' => 'required|unique:mysql_2.cadastros,apelido,' . (\Auth::user()->codigo  ?? 0).',codigo',
            'celular' => 'required_without:telefone',
            'desc_como_chegou' => 'required_if:como_chegou,"5"',
            'cep' => 'required',
            'endereco' => 'required',
            'numero' => 'required',
            'bairro' => 'required',
            'cidade' => 'required',
            'estado' => 'required',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'nome.required_unless' => 'Você deve informar seu nome.',
            'conjuge.required_if' => 'Nome do conjuge deve ser informado',
            'c_cpf.required_if' => 'CPF do conjuge deve ser informado',
            'c_rg.required_if' => 'RG do conjuge deve ser informado',
            'filiacao.required_unless' => 'Você deve informar o nome da sua mãe',
            'responsavel_cadastro.required_unless' => 'Você deve informar o nome do responsável pelo cadastro',
            'data_nascimento.required_unless' => 'Informe a data de seu nascimento.',
            'data_nascimento.date_format' => 'A data de nascimento informada não é válida.',
            'cpf.required_unless'  => 'Informe seu CPF.',
            'cpf.unique'  => 'O CPF informado já foi cadastrado anteriormente.',
            'cnpj.unique'  => 'O CNPJ informado já foi cadastrado anteriormente.',
            'rg.required_unless'  => 'Informe seu RG.',
            'profissao.required_unless'  => 'Informe sua profissão.',
            'razao_social.required_unless'  => 'Informe a razão social da empresa.',
            'cnpj.required_unless'  => 'Informe o CNPJ da empresa.',
            'socio.required_unless'  => 'Informe o nome de um dos sócios da empresa.',
            's_cpf.required_unless'  => 'Informe o CPF do sócio já informado no campo acima.',
            's_rg.required_unless'  => 'Informe o RG do sócio já informado no campo acima.',
            'email.required'  => 'Você deve informar um e-mail válido.',
            'email.unique' => 'O endereço :input já está cadastrado em nossa base de dados.',
            'apelido.unique' => 'O apelido :input já está sendo usado por outro usuário do site. Informe outro apelido.',
            'apelido.unique' => 'O apelido :input já está sendo usado por outro usuário do site. Informe outro apelido.',
            'celular.required_without' => 'Informe ao menos número de telefone para contato.',
            'cep.required' => 'Informe o seu CEP.',
            'endereco.required' => 'Informe seu endereço.',
            'numero.required' => 'Informe o número do endereço informado.',
            'bairro.required' => 'Informe o bairro.',
            'cidade.required' => 'Informe a cidade.',
            'estado.required' => 'Informe o estado.',
            'desc_como_chegou.required_if' => 'Descreva como chegou no site',
        ];
    }
}
