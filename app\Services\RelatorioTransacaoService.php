<?php

namespace App\Services;

class RelatorioTransacaoService {
    public function exportaTransacoesLeilao(\App\Lotes $lote) {
        $csv = "Relatório de transações pelo sistema de leilão;;;\r\n";
	$csv  .= "Data;Agente;Ação\r\n";
	if ($lote->leilao->created_at) {
	    $csv .= $lote->leilao->created_at->format('d/m/Y H:i:s') . ";Funcionário;Leilão incluído na plataforma\r\n";
        }
	else
	{
	    $csv .= $this->formataData($lote->leilao->data_cadastro, false) . ";Funcionário;Leilão incluído na plataforma\r\n";

	}

        // abertura

            $dataHora = $this->formataData($lote->leilao_data_inicial . " " . $lote->leilao_hora_inicial);
            $csv .= $dataHora . ";Leiloeiro(a);Abertura do leilão.\r\n";

        // Habilitados
        foreach($lote->listaHabilitados as $habilitado) {
            $apelidoNome = $habilitado->cadastro->apelido ?? $habilitado->cadastro->nome ?? $habilitado->cadastro->razao_social;
            $csv .= $this->formataData($habilitado->data_habilitacao) . ";Proponente;Pedido de habilitação do usuário @$apelidoNome\r\n";

            foreach ($habilitado->lances($habilitado->idlote)->get() as $lance) {
                $csv .= $this->formataData($habilitado->data_habilitacao). ";Proponente;Lance do usuário $apelidoNome no valor de  R$ " . number_format($lance->valor, 2, ",", ".") . "\r\n";
            }
        }

        // encerramento
        if ($lote->encerrado == '6' || $lote->encerrado == '2' || $lote->encerrado == '3' || $lote->encerrado == '8') {
            $dataHora = $lote->leilao2_data_final . " " . $lote->leilao2_hora_final;
            $csv .= $this->formataData($dataHora) . ";Leiloeiro(a);Encerramento do leilão\r\n";
        }
        $dir = __DIR__ . '/../../tmp';
        if (!is_dir($dir)) {
            mkdir($dir);
        }
        $fileName = $dir . '/transacao-lote-' . $lote->codigo . '.csv';
        if (file_exists($fileName)) {
            unlink($fileName);
        }
        $arq = fopen($fileName, 'a+');
        fwrite($arq, $csv);
        fclose($arq);
        return $csv;
    }


    private function formataData($data, $hora = true) {
	if (!$hora) {
            return \Carbon\Carbon::createFromFormat('Y-m-d', $data)->format('d/m/Y');
	}
        return \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $data)->format('d/m/Y H:i:s');
    }

}
