<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLeiloesCurtidos extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('leiloes_curtidos', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('idusuario');
            $table->unsignedInteger('idleilao');
            $table->unsignedInteger('idlote');
            $table->timestamps();

            $table->foreign('idusuario')->references('codigo')->on('cadastros')->onDelete('cascade');
            $table->foreign('idleilao')->references('codigo')->on('leiloes')->onDelete('cascade');
            $table->foreign('idlote')->references('codigo')->on('lotes')->onDelete('cascade');

            $table->index(['idusuario', 'idleilao',  'idlote']);

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('leiloes_curtidos');
    }
}
