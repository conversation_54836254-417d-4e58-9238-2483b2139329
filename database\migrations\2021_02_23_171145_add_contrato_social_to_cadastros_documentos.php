<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddContratoSocialToCadastrosDocumentos extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('cadastros_documentos', function (Blueprint $table) {
            $table->string('contrato_social', 150);
            $table->boolean('status_contrato_social');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('cadastros_documentos', function (Blueprint $table) {
            $table->dropColumn('contrato_social');
            $table->dropColumn('status_contrato_social');
        });
    }
}
