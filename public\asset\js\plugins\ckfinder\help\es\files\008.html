<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
	"http://www.w3.org/TR/html4/loose.dtd">
<html lang="es">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<title>Guía del usuario de CKFinder</title>
	<link href="../../files/other/help.css" type="text/css" rel="stylesheet">
	<script type="text/javascript" src="../../files/other/help.js"></script>
	<meta name="robots" content="noindex, nofollow" />
</head>
<body>
	<h1>
		Bot&oacute;n Configuraci&oacute;n </h1>
	<p>
		El bot&oacute;n "Configuraci&oacute;n", en <a href="005.html">la barra de herramientas</a>, abre el &quot;Panel de Configuraci&oacute;n&quot;, donde usted podr&aacute; configurar y personalizar CKFinder, he aqu&iacute; una imagen de &eacute;l:</p>
	<p style="text-align: center">
		<img src="images/013.png" width="416" height="189" alt="Panel de Configuración" /></p>
	<p>
		Todos los ajustes son guardados automaticamente utilizando las denominadas &quot;Cookies&quot; de su navegador. Las &quot;Cookies&quot; son peque&ntilde;os ficheros que contienen informaci&oacute;n de configuraci&oacute;n privada para sitios web especificos.</p>
	<p>
		Para cerrar el panel de configuraci&oacute;n, haga click sobre el bot&oacute;n &quot;Cerrar&quot; o haga click nuevamente sobre el bot&oacute;n &quot;Configuraci&oacute;n&quot; de la barra de herramientas.</p>
	<h2>
		Opciones de Configuraci&oacute;n </h2>
	<p>
		Todas las opciones de configuraci&oacute;n tienen que ver con el <a href="004.html">Panel de Ficheros </a>.
		Se usan para controlar como mostrar informaci&oacute;n en ese panel. El panel de Ficheros reacciona inmediatamente a los cambios hechos en el panel de configuraci&oacute;n.</p>
	<h3>
		Vista</h3>
	<p>
		Controla el modo actual de vista del <a href="004.html">Panel de Ficheros</a>:</p>
	<ul>
		<li>El modo "<strong>Iconos</strong>" mostrar&aacute; cada fichero como una &quot;caja&quot;. Para las imagenes, una peque&ntilde;a previsualizaci&oacute;n de ella (llamada miniatura) ser&aacute; mostrada dentro de la caja. Para otros tipos de fichero, un icono ser&aacute; mostrado en su lugar.</li>
	</ul>
	<ul>
		<li>El modo "<strong>Lista</strong>" mostrar&aacute; todos los ficheros como una lista, uno abajo del otro. No se mostrar&aacute; ning&uacute;n tipo de previsualizaci&oacute;n en este modo.</li>
	</ul>
	<h3>
		Mostrar</h3>
	<p>
		Ajusta la cantidad de informaci&oacute;n disponible en el panel de ficheros. Para ejemplificar, he aqu&iacute; como se mostrar&iacute;an los ficheros cuando ning&uacute;na opci&oacute;n est&aacute; seleccionada, hasta llegar a tener todas las opciones seleccionadas.</p>
	<table align="center" cellpadding="0" cellspacing="0">
			<tr>
				<td valign="top" style="padding-right: 10px">
					<img src="images/014.gif" width="112" height="112" alt="Imagen"/></td>
				<td valign="top" style="padding-right: 10px; padding-left: 10px">
					<img src="images/015.gif" width="112" height="128" alt="Imagen y nombre" /></td>
				<td valign="top" style="padding-right: 10px; padding-left: 10px">
					<img src="images/016.gif" width="112" height="144" alt="Imagen, nombre y fecha" /></td>
				<td valign="top" style="padding-left: 10px">
					<img src="images/017.gif" width="112" height="160" alt="Imagen, nombre, fecha y peso" /></td>
			</tr>
	</table>

	<h3>
		Ordenar</h3>
	<p>
		Controla el orden en el que los ficheros ser&aacute;n listados. Puede ser alfabeticamente por el nombre del fichero, por fecha de creaci&oacute;n del fichero siendo el  m&aacute;s nuevo el primero o incluso por el tama&ntilde;o del fichero.</p>
</body>
</html>
