<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Contracts\Auditable;

class Audit extends Model implements Auditable
{
    use Notifiable;
    use \OwenIt\Auditing\Auditable;
    
    protected $dates = [
        'created_at'
    ];

    const EVENTS = [
        'updated' => 'Atualização',
        'created' => 'Novo registro',
        'deleted' => 'Exclusão',
    ];

    function eventType() {
        return self::EVENTS['' . $this->event . ''];
    }

    function generatingEntity() {
        $entity = $this->user_type;
        if ($entity === null) {
            $entity = $this->url;
            if ($this->url === 'console') {
                return 'Evento do sistema';
            }
            return 'Visitante não logado';
        }
        return $entity;
    }

}