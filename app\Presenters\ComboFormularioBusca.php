<?php

namespace App\Presenters;

use App\Lotes;

class ComboFormularioBusca {

    public static function composeSearch($input) {
        $idestado = 0;
        $idcidade = 0;
        $bairro =  '';
        $uf = '';
        $nomeCidade = '';
        $bairroPost = @$input['bairro'];
        $valor = isset($input['valor']) ? (int) $input['valor'] : '';
        $idcategoria = isset($input['idcategoria']) ? (int) $input['idcategoria'] : '';
        $subcategoria = isset($input['idsubcategoria']) ? (int) $input['idsubcategoria'] : '';

        if (isset($input['idestado']) &&  strstr($input['idestado'], '|')) {
            $input['idestado'] = explode('|', $input['idestado']);
            $idestado = $input['idestado'] != 0 ? $input['idestado'][1] : 0;
            $uf = $input['idestado'] != 0 ? $input['idestado'][0] : '';
        }

        if (isset($input['idcidade']) && strstr($input['idcidade'], '|')) {
            $input['idcidade'] = explode('|', $input['idcidade']);
            $idcidade = (int) $input['idcidade'][1];
            $nomeCidade = $idcidade  > 0 ? $input['idcidade'][0] : '';
        }

        return ['estados' => self::cacheEstados(),
                'cidades' => self::cacheCidades($idestado),
                'bairros' => self::cacheBairros($idcidade, $idestado),
                'idestado' => $idestado,
                'idcidade' => $idcidade,
                'nomeCidade' => $nomeCidade,
                'bairroPost' => $bairroPost,
                'valor' => $valor,
                'idcategoria' => $idcategoria,
                'subcategoria' => $subcategoria,
                'uf' => $uf];

    }

    private static function cacheCidades($idestado) {
        return Lotes::cidadesDisponiveis($idestado)['cidades'];
    }

    private static function cacheBairros($idcidade, $idestado) {
        return Lotes::bairrosDisponiveis($idcidade, $idestado)['bairros'];
    }

    private static function cacheEstados() {
        return Lotes::estadosDisponiveis()['estados'];
    }

}