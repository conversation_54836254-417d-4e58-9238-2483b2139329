<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title><PERSON>K<PERSON><PERSON></title><meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<link href="../../files/other/help.css" type="text/css" rel="stylesheet" />
	<script type="text/javascript" src="../../files/other/help.js"></script>
	<meta name="robots" content="noindex, nofollow" />
</head>
<body>
	<h1>
		Asetukset-nappi
	</h1>
	<p>
		"Asetukset"-nappi <a href="005.html">työkalurivillä</a> avaa "Asetusruudun",
		jossa voit konfiguroida ja muokata CKFinderia. Tässä kuvaruutukaappaus:
	</p>
	<p style="text-align: center">
		<img src="images/013.gif" height="190" width="404" /></p>
	<p>
		Kaikki asetukset tallennetaan hyödyntäen selaimen evästeitä (cookies).
		Evästeet, "keksit", ovat pieniä tiedostoja, jotka sisältävät käyttäjäkohtaisia
		konfigurointiasetuksia. Kullekin sivustolle on oma evästeensä. </p>
	<p>
		Napsauta "Sulje"-nappia tai "Asetukset"-nappia toistamiseen sulkeaksesi asetusruudun.</p>
	<h2>
		Konfigurointivalinnat</h2>
	<p>
		Kaikki konfigurointivalinnat liittyvät <a href="004.html">tiedostoruutuun</a>.
		Niitä käytetään tietoruudun informaation esittämistavan valitaan. Tiedostoruutu reagoi
		välittömästi asetusmuutoksiin. </p>
	<h3>
		Näkymä</h3>
	<p>
		Hallinnoi näkymätyypin valintaa <a href="004.html">tiedostoruudussa</a>:</p>
	<ul>
		<li>"<strong>Esikatselukuvat</strong>" näyttää jokaisen tiedoston "laatikkona".
		Vain kuvat näytetään pienoiskoossa, muille tiedostoille näkyy kuvake. </li>
	</ul>
	<ul>
		<li>"<strong>Luettelo</strong>" näyttää kaikki tiedostot alekkaisena listana.
		Esikatselukuvia ei näytetä.</li>
	</ul>
	<h3>
		Näytä
	</h3>
	<p>
		Asettaa näytettävän informaation määrän tiedostoruudulle. Esimerkkinä näytetään alla
		vaihtoehdot eri informaatiomäärävalinnoilla:</p>
	<p style="text-align:center">
		<table align="center" cellpadding="0" cellspacing="0">
			<tr>
				<td valign="top" style="padding-right: 10px">
					<img src="images/014.gif" width="112" height="112" /></td>
				<td valign="top" style="padding-right: 10px; padding-left: 10px">
					<img src="images/015.gif" width="112" height="128" /></td>
				<td valign="top" style="padding-right: 10px; padding-left: 10px">
					<img src="images/016.gif" width="112" height="144" /></td>
				<td valign="top" style="padding-left: 10px">
					<img src="images/017.gif" width="112" height="160" /></td>
			</tr>
		</table>
	</p>
	<h3>
		Lajittele</h3>
	<p>
		Asettaa järjestyksen, jossa tiedostot näytetään: aakkosjärjestyksessä tiedostonimien
		perusteella, tiedoston luontipäivämäärän mukaan (uusimmat ensin) tai tiedostokoon
		mukaisesti. </p>
</body>
</html>
