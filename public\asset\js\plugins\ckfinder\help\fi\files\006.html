<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>CK<PERSON><PERSON></title><meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<link href="../../files/other/help.css" type="text/css" rel="stylesheet" />
	<script type="text/javascript" src="../../files/other/help.js"></script>
	<meta name="robots" content="noindex, nofollow" />
</head>
<body>
	<h1>
		Lataa palvelimelle -nappi
	</h1>
	<p>
		Lataa palvelimelle -nappi* <a href="005.html">työkalurivillä</a> avaa
		"Lataa palvelimelle" -ru<PERSON><PERSON>, jonka avulla lisätään uusia tiedostoja valittuun kansioon.
		Tässä kuvaruutukaappaus: </p>
	<p style="text-align: center">
		<img height="153" src="images/012.gif" width="404" />&nbsp;</p>
	<p>
		Napsauta "Peru"-nappia sulkeaksesi ruudun, tai napsauta uudelleen "Lataa palvelimelle"-nappia
		työkalurivillä.</p>
	<p>
		* "<strong>Lataa palvelimelle</strong>" -termi tarkoittaa tiedoston siirtämistä paikalliselta
		tietokoneelta palvelimelle.</p>
	<h2>
		Latauksen vaiheet</h2>
	<ol>
		<li>Valitse tiedosto koneeltasi "Selaa..."-napilla. Napin teksti vaihtelee selaimittain,
			mutta se sijaitsee aina heti kentän "Valitse ladattava tiedosto" jälkeen.</li>
		<li>Napsauta nappia "Lataa valittu tiedosto". Lataamisesta kertova viesti-ikkuna ilmestyy näkuviin.
		</li>
		<li>Lataamisen loputtua Lataa palvelimelle -ruutu sulkeutuu automaattisesti ja ladattu tiedosto
			muuttuu aktiiviseksi <a href="004.html">tiedostoruudussa</a>.</li>
	</ol>
	<h2>
		Latausviestit</h2>
	<p>
		Voit nähdä seuraavia viestejä lataustapahtuman aikana:</p>
	<h3>
		Samanniminen tiedosto on jo olemassa. Palvelimelle ladattu tiedosto on nimetty:
		"tiedostonimi(1).xxx"</h3>
	<p>
		Viesti tarkoittaa, että samanniminen kuin lataamasi tiedosto on jo tallennettu hakemistoon.
		Ristiriidan estämiseksi tiedostonimen perään lisätään juokseva numero "(1)".</p>
	<h3>
		Tiedosto ei kelpaa</h3>
	<p>
		Palvelin ei hyväksy ladattavaa tiedostoa. </br></br>
		Yleensä kysymys on siitä, että CKFinder on asetettu estämään tiettyjen tiedostotyyppien
		lataaminen. Ne tunnistetaan tiedostotarkenteen perusteella. Lataaminen
		estetään tietoturvasyistä. Vaihtoehtoisesti tiedostokoko voi olla liian suuri. Siinä
		tapauksessa palvelin on konfiguroitava hyväksymään isompia tiedostoja.
	</p>
	<h3>Palvelimelle lataaminen on peruttu turvallisuussyistä. Tiedosto sisältää HTML-tyylistä dataa.</h3>
	<p>Ladattava tiedosto sisältää HTML-koodia. Tietoturvasyistä vain määrätyillä tiedostotarkenteilla
		varustetut tiedostot saavat sisältää HTML-koodia.</br></br>

		Ota yhteyttä pääkäyttäjään selvittääksesi, mitkä ovat hyväksyttäjä tiedostotyyppejä, ja mikä
		on niiden suurin sallittu koko.</p>
</body>
</html>
