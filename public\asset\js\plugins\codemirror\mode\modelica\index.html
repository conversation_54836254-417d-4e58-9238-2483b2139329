<!doctype html>

<title>CodeMirror: Modelica mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="../../addon/edit/matchbrackets.js"></script>
<link rel="stylesheet" href="../../addon/hint/show-hint.css">
<script src="../../addon/hint/show-hint.js"></script>
<script src="modelica.js"></script>
<style>.CodeMirror {border: 2px inset #dee;}</style>
<div id=nav>
  <a href="http://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png"></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">Modelica</a>
  </ul>
</div>

<article>
<h2>Modelica mode</h2>

<div><textarea id="modelica">
model BouncingBall
  parameter Real e = 0.7;
  parameter Real g = 9.81;
  Real h(start=1);
  Real v;
  Boolean flying(start=true);
  Boolean impact;
  Real v_new;
equation
  impact = h <= 0.0;
  der(v) = if flying then -g else 0;
  der(h) = v;
  when {h <= 0.0 and v <= 0.0, impact} then
    v_new = if edge(impact) then -e*pre(v) else 0;
    flying = v_new > 0;
    reinit(v, v_new);
  end when;
  annotation (uses(Modelica(version="3.2")));
end BouncingBall;
</textarea></div>

    <script>
      var modelicaEditor = CodeMirror.fromTextArea(document.getElementById("modelica"), {
        lineNumbers: true,
        matchBrackets: true,
        mode: "text/x-modelica"
      });
      var mac = CodeMirror.keyMap.default == CodeMirror.keyMap.macDefault;
      CodeMirror.keyMap.default[(mac ? "Cmd" : "Ctrl") + "-Space"] = "autocomplete";
    </script>

    <p>Simple mode that tries to handle Modelica as well as it can.</p>

    <p><strong>MIME types defined:</strong> <code>text/x-modelica</code>
    (Modlica code).</p>
</article>
