<?php
// Copyright (c) 2003-2013, CKSource - <PERSON><PERSON>. All rights reserved.
// For licensing, see LICENSE.html or http://ckfinder.com/license

//  Defines the object for the Spanish language.

$GLOBALS['CKFLang'] = array (
	'ErrorUnknown' => 'No ha sido posible completar la solicitud. (Error %1)',
	'Errors' => array (
		'10' => 'Comando incorrecto.',
		'11' => 'El tipo de recurso no ha sido especificado en la solicitud.',
		'12' => 'El tipo de recurso solicitado no es válido.',
		'102' => 'Nombre de fichero o carpeta no válido.',
		'103' => 'No se ha podido completar la solicitud debido a las restricciones de autorización.',
		'104' => 'No ha sido posible completar la solicitud debido a restricciones en el sistema de ficheros.',
		'105' => 'La extensión del archivo no es válida.',
		'109' => 'Petición inválida.',
		'110' => 'Error desconocido.',
		'111' => 'It was not possible to complete the request due to resulting file size.',
		'115' => 'Ya existe un fichero o carpeta con ese nombre.',
		'116' => 'No se ha encontrado la carpeta. Por favor, actualice y pruebe de nuevo.',
		'117' => 'No se ha encontrado el fichero. Por favor, actualice la lista de ficheros y pruebe de nuevo.',
		'118' => 'Las rutas origen y destino son iguales.',
		'201' => 'Ya existía un fichero con ese nombre. El fichero subido ha sido renombrado como "%1".',
		'202' => 'Fichero inválido.',
		'203' => 'Fichero inválido. El peso es demasiado grande.',
		'204' => 'El fichero subido está corrupto.',
		'205' => 'La carpeta temporal no está disponible en el servidor para las subidas.',
		'206' => 'La subida se ha cancelado por razones de seguridad. El fichero contenía código HTML.',
		'207' => 'El fichero subido ha sido renombrado como "%1".',
		'300' => 'Ha fallado el mover el(los) fichero(s).',
		'301' => 'Ha fallado el copiar el(los) fichero(s).',
		'500' => 'El navegador de archivos está deshabilitado por razones de seguridad. Por favor, contacte con el administrador de su sistema y compruebe el fichero de configuración de CKFinder.',
		'501' => 'El soporte para iconos está deshabilitado.',
	)
);
