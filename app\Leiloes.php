<?php

namespace App;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Contracts\Auditable;

class Leiloes extends Model implements Auditable
{
    use Notifiable;
    use SoftDeletes;
    use \OwenIt\Auditing\Auditable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'tipo',
        'modalidade',
        'modelo',
        'idleiloeiro',
        'habilitacao',
        'titulo',
        'subtitulo',
        'numero',
        'publicacao',
        'jornal',
        'edital',
        'leilao_data_tipo',
        'leilao_data_inicial',
        'leilao_hora_inicial',
        'leilao_data_final',
        'leilao_hora_final',
        'leilao2_data_tipo',
        'leilao2_data_inicial',
        'leilao2_hora_inicial',
        'leilao2_data_final',
        'leilao2_hora_final',
        'idcomitente',
        'forum',
        'juiz',
        'responsavel',
        'endereco',
        'cidade',
        'visitacao',
        'encerrado',
        'logo',
        'destaque',
        'condicoes',
        'regras',
        'restrito',
        'suspender',
        'data_cadastro',
        'ind_status',
        'imagem_360',
        'youtube',
        'usar_cronometro',
        'status_leilao',
        'desconto',
        'condicao',
        'desconto_p',
        'delay_encerramento_lotes',
        'parceiro',
        'url_parceiro',
        'cod_referencia',
        'data_suspensao',
        'proposta_pos_encerramento'
    ];

    protected $table = 'leiloes';
    protected $primaryKey = 'codigo';
    protected $dates = ['deleted_at', 'data_suspensao'];

    public function __construct() {
        parent::__construct();
        $middlewares = (\Route::current());

        if ($middlewares) {
            $middlewares = $middlewares->gatherMiddleware();
            if (in_array('auth', $middlewares) == true) {
                if(session()->has('portaldb')) {
                    $this->table = session()->get('portaldb') . '.' . $this->table;
                }
            }
        }
    }

    public function linksLotes() {
        $lotes = \App\Lotes::where('idleilao', $this->codigo)->orderBy('numero_lote')->get();
        $arrayLinks = [];
        foreach($lotes as $lote) {
            $arrayLinks['/leilao/lote/' . $lote->codigo .'/' . urlTitulo($lote->titulo)] =  [$lote->codigo, 'Lote ' . $lote->numero_lote, $this->codigo];

        }

        return $arrayLinks;
    }

    public function lots()
    {
        return $this->hasMany('App\Lotes', 'idleilao', 'codigo')->orderBy('categoria', 'asc');
    }

    public function setTable($name) {
        $this->table = $name;
        return $this;
    }
}