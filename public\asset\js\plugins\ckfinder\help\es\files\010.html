<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
	"http://www.w3.org/TR/html4/loose.dtd">
<html lang="es">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<title>Guía del usuario de CKFinder</title>
	<link href="../../files/other/help.css" type="text/css" rel="stylesheet">
	<script type="text/javascript" src="../../files/other/help.js"></script>
	<meta name="robots" content="noindex, nofollow" />
</head>
<body>
	<h1>
	Barra de Estado</h1>
	<p>
		La "Barra de Estado" es un peque&ntilde;o espacio usado para mostrar informaci&oacute;n relevante al fichero seleccionado, el n&uacute;mero total de ficheros en la carpeta, etc... Se puede encontrar al final de la interfaz de CKFinder</p>
	<p>
		Si <strong>un fichero es seleccionado</strong> en CKFinder, la barra de estado mostrar&aacute; informaci&oacute;n detallada acerca de ese fichero en particular, contendr&aacute; el nombre del fichero, su tama&ntilde;o y la fecha de su &uacute;ltima modificaci&oacute;n, por ejemplo: </p>
	<p style="text-align: center">
		<img src="images/018.png" width="257" height="16" alt="adivina.xls (2444Kb 21/03/2011)"/>&nbsp;</p>
	<p>Si por el contrario <strong> ning&uacute;n fichero est&aacute; seleccionado</strong>, se mostrar&aacute; el n&uacute;mero total de ficheros en la carpeta actual. Por ejemplo: </p>
	<p style="text-align: center">
		<img src="images/019.png" width="145" height="15" alt="2 ficheros"/>&nbsp;</p>
</body>
</html>
