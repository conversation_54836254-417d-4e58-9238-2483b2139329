<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>CKFinder User's Guide</title>
	<link href="../../files/other/help.css" type="text/css" rel="stylesheet" />
	<script type="text/javascript" src="../../files/other/help.js"></script>
	<meta name="robots" content="noindex, nofollow" />
</head>
<body>
	<h1>
		Files Pane</h1>
	<p>
		The <strong>Files Pane</strong> lists all files available in the selected folder.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_file_pane.png" width="451" height="429" alt="CKFinder Files Pane" />&nbsp;</p>
	<h2>
		Different Views</h2>
	<p>
		The <strong>Files Pane</strong> may present the files with two different display formats,
		depending on CKFinder settings (see "<strong><a href="008.html">Settings</a></strong>").
		The following is a comparison of the <strong>Thumbnails</strong> and the <strong>List</strong>
		views for the same folder.</p>
	<p>
		When you configure CKFinder to use the <strong>Thumbnails</strong> view, the files will
		be presented as thumbnails (miniature previews or icons), with or without additional
		information like file name, size, or date (depending on your settings).</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_file_view_thumbnails.png" width="531" height="178" alt="CKFinder Files Pane with Thumbnails view" />&nbsp;</p>
	<p>
		When you configure CKFinder to use the <strong>List</strong> view, the files will
		be presented in a list, with or without additional information like file name, size,
		or date (depending on your settings).</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_file_view_list.png" width="564" height="136" alt="CKFinder Files Pane with List view" />&nbsp;</p>
	<h2>
		Basic Operations</h2>
	<h3>
		Activating (Selecting) a File with a Mouse</h3>
	<p>
		In order to activate a file and make it the "current file" in CKFinder, click the file
		name or its thumbnail. To make it easier to choose the file, when you hover your mouse
		over a file, its background will become highlighted. Once a file is activated, it will be
		permanently highlighted with a different background color.</p>
	<p>
		The figure below presents various states of a file as viewed in a folder: unselected, hovered
		with a mouse, and activated (selected).</p>
	<table align="center" cellpadding="0" cellspacing="0">
		<tr>
			<td valign="top" style="padding-right: 10px">
				<img src="../../files/images/CKFinder_file_select_unselected.png" width="130" height="166" alt="Unselected file in CKFinder" /></td>
			<td valign="top" style="padding-right: 10px; padding-left: 10px">
				<img src="../../files/images/CKFinder_file_select_hovered.png" width="130" height="166" alt="Hovered file in CKFinder" /></td>
			<td valign="top" style="padding-right: 10px; padding-left: 10px">
				<img src="../../files/images/CKFinder_file_select_selected.png" width="130" height="166" alt="Selected file in CKFinder" /></td>
		</tr>
	</table>
	<p>&nbsp;</p>
	<p>
		In order to deactivate a file, click another one (activating it) or click any empty space inside
		the <strong>Files Pane</strong>.</p>
	<h3>
		Copying a File</h3>
	<p>In order to copy a file to a different folder, select it, and drag onto the target folder in the
		<strong>Folders Pane</strong>. When you drop it onto a folder name, choose the <strong>
		Copy File Here</strong> option from the context menu.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_file_drag_copy.png" width="130" height="54" alt="Copying a file in CKFinder" />&nbsp;</p>
	<p>
		The file will be duplicated and its copy will be placed in the target folder. The
		source folder will remain untouched.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_file_copied.png" width="304" height="149" alt="File copied in CKFinder" />&nbsp;</p>
	<p>
		<span class="info">Note:</span> If the file with the same name already exists in the
		target folder, you will be prompted to choose between overwriting it or automatically
		renaming the copied file (this is the default option).</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_file_already_exists.png" width="304" height="190" alt="Error message for a file that already exists in CKFinder" />&nbsp;</p>
	<h3>
		Moving a File</h3>
	<p>In order to move a file to a different folder, select it, and drag onto the target folder
		in the <strong>Folders Pane</strong>. When you drop it onto a folder name, choose the
		<strong>Move File Here</strong> option from the context menu.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_file_drag_move.png" width="130" height="54" alt="Moving a file in CKFinder" />&nbsp;</p>
	<p>
		The file will be removed from the source folder and pasted into the target folder.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_file_moved.png" width="304" height="149" alt="File moved in CKFinder" />&nbsp;</p>
	<h2>
		Advanced Operations</h2>
	<p>
		Advanced operations can be performed on a file by using its <strong><a href="012.html">Context
		Menu</a></strong>. Depending on the circumstances, the following options may be available:</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_file_menu.png" width="130" height="156" alt="File context menu in CKFinder" />&nbsp;</p>
	<ul>
		<li><strong>Select</strong> &ndash; selects the file.</li>
		<li><strong>View</strong> &ndash; displays the full-sized file in a new browser tab or window.</li>
		<li><strong>Download</strong> &ndash; downloads the file from the server to your computer.</li>
		<li><strong>Resize</strong> &ndash; allows you to modify file size and/or create a new thumbnail.</li>
		<li><strong>Rename</strong> &ndash; changes the name of the file.</li>
		<li><strong>Delete</strong> &ndash; permanently removes the file.</li>
	</ul>
	<p>
		<span class="info">Note:</span> Some context menu options may be disabled (and thus
		grayed out), depending on CKFinder settings enforced by your system administrator.</p>
	<h3>
		Selecting a File</h3>
	<p>
		In order to select a file by using the context menu, choose the <strong>Select</strong>
		option. Alternatively, you can also perform a double-click on the file with your mouse.</p>
	<p>
		Depending on the environment where CKFinder is used, the selection operation can, for example,
		send the file URL to another application or insert an image directly into an article created
		in your CMS system.</p>
	<h3>
		Viewing (Previewing) a File</h3>
	<p>
		In order to preview a file in the browser, choose the <strong>View</strong> option from
		its context menu. Not all kinds of files can be viewed in browsers, but this feature
		is quite useful for images, text, and PDF files. In other cases, the browser will ask
		you to open the file with an appropriate application.</p>
	<p>
		When you attempt to preview an image, CKFinder will display in directly in a lightbox popup.</p>
	<h3>
		Downloading a File</h3>
	<p>
		In order to download a file, choose the <strong>Download</strong> option from its context
		menu. The browser will ask you for a location on your computer to save the downloaded file.
		Depending on your browser and operating system settings, the file might also be opened
		immediately after it is downloaded by using an appropriate application of your operating
		system.</p>
	<h3>
		Resizing a File (Image)</h3>
	<p>
		In order to change the image size or its thumbnail size, choose the <strong>Resize</strong>
		option from its context menu. Once you enter new image dimensions or choose a thumbnail size,
		close the dialog window to apply the changes.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_file_resize.png" width="424" height="319" alt="Resizing an image in CKFinder" />&nbsp;</p>
	<p>If the image is too big, you can alter its dimensions by entering new values into the
		<strong>Width</strong> and <strong>Height</strong> fields. By default the image ratio is locked,
		which you can see thanks to the <img src="../../files/images/CKFinder_resize_lock.png" width="7" height="8" alt="Image size ratio locked in CKFinder" />
		(<strong>Lock</strong>) button. This means that when you change one of the size values (width
		or height), the other one will be adjusted automatically.</p>
	<p>If you want to freely modify both dimensions, click the <strong>Lock</strong> button in order to
		unlock the ratio. The button will now change to
		<img src="../../files/images/CKFinder_resize_unlock.png" width="7" height="10" alt="Image size ratio unlocked in CKFinder" />
		(<strong>Unlock</strong>) and modification of one dimension will not automatically cause the
		other one to be adjusted. To lock the image ratio again, click the <strong>Unlock</strong> button
		once more.</p>
	<p>You can easily return to original image size by pressing the
		<img src="../../files/images/CKFinder_resize_reset.png" width="11" height="11" alt="Image size ratio reset in CKFinder" />
		(<strong>Reset Size</strong>) button. This will reset the image size; the original width and height
		will now appear in appropriate text boxes.</p>
	<p>
		If you resize the image, you can decide to save it under the same name, overwriting the
		existing file. Please note that this operation cannot be undone and once you save the file with its
		modified dimensions, you will not be able to restore the original size. In order to overwrite the
		original image, leave the <strong>Create a new image</strong> checkbox unselected.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_file_resize_overwrite.png" width="424" height="319" alt="Overwriting a resized image in CKFinder" />&nbsp;</p>
	<p>
		You can also decide to save the resized image in a new file and leave the original as is. If you
		select the <strong>Create a new image</strong> checkbox, you will be able to give the modified
		file a new name. By default CKFinder suggests to save the image under the name that is built from
		the original file name and new file dimensions (for example: <code>Sun1_100x100.jpg</code> when the
		original file was named <code>Sun1.jpg</code> and the file was resized to 100 pixels wide and 100
		pixels high).</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_file_resize_new.png" width="424" height="319" alt="Creating a new resized image in CKFinder" />&nbsp;</p>
	<p>
		When you are ready with your changes, click the <strong>OK</strong> button to close the dialog
		window. On successful resizing a confirmation message will be displayed.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_file_resize_resized.png" width="304" height="149" alt="File resized in CKFinder" />&nbsp;</p>
	<p>
		If you saved the resized image in a new file, you will see both files appear inside the folder.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_file_resize_both.png" width="268" height="170" alt="Original and resized file in CKFinder" />&nbsp;</p>
	<p>
		<span class="info">Note:</span> The size of the original image may limit the options available for
		the <strong>Resize</strong> feature. The modified image size cannot exceed the original dimensions.
		</p>
	<h3>
		Generating Thumbnails</h3>
	<p>
		The <strong>Resize</strong> feature can also be used in order to generate resized copies of
		images (thumbnails) that you can use in articles on your website or in blog posts. Thanks to
		the thumbnail resizing feature the site administrator can set predefined thumbnail size
		options that can be used by CKFinder users. This will save you the effort to manually
		enter modified width and height values for each image and will let you keep the thumbnail
		sizes consistent across your site.</p>
	<p>
		In order to use one of the predefined thumbnail sizes, choose the <strong>Resize</strong>
		option from the image context menu. In the <strong>Create a new thumbnail</strong> section
		of the <strong>Resize</strong> dialog window select the checkbox(es) representing the
		desired thumbnail size(s). On successful resizing a confirmation message will be displayed.
		The resized image copies will be created in the same folder and the new files will be named
		according to the thumbnail size option that you choose (with <code>_large</code>,
		<code>_medium</code> and <code>_small</code> suffixes added to the original file name).</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_file_resize_thumbnails.png" width="398" height="170" alt="Thumbnails generated in CKFinder" />&nbsp;</p>
	<p>
		<span class="info">Note:</span> If the image is smaller than some of the thumbnails options
		(see example above), the ones that are unavailable will be grayed out.</p>
	<h3>
		Renaming a File</h3>
	<p>
		In order to rename a file, choose the <strong>Rename</strong> option from its context menu
		or use the <em>F2</em> keyboard shortcut. Type the new file name in the dialog window that
		will be displayed, overwriting the existing name. Once you enter the new file name and close
		the dialog window, the file will be renamed.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_file_rename.png" width="304" height="149" alt="Renaming a file in CKFinder" />&nbsp;</p>
	<p>
		Not all characters can be used in folder and file names due to limitations of the
		systems where CKFinder runs. Among the characters that cannot be used in folders and
		files names are: <code>\</code> <code>/</code> <code>:</code> <code>*</code>
		<code>?</code> <code>&quot;</code> <code>&lt;</code> <code>&gt;</code> and <code>|</code>.</p>
	<p>
		<span class="warning">Attention:</span> When you rename a file, links or media insertions
		available on other pages and pointing to the renamed file will be broken, and thus not
		available anymore. Because of that be careful when using this feature.</p>
	<h3>
		Deleting a File</h3>
	<p>
		In order to delete a file, choose the <strong>Delete</strong> option from its context menu
		or use the <em>Del</em> key. A confirmation message will appear to ensure that this operation
		is what you really intend to do. Once you confirm the deletion, the file will be removed.</p>
	<p>
		<span class="warning">Attention:</span> This operation cannot be undone. Once you delete the
		file, you will not be able to restore it.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_file_delete.png" width="304" height="149" alt="Deleting a file in CKFinder" />&nbsp;</p>
	<p>
		<span class="warning">Attention:</span> When you delete a file, links or media insertions
		available on other pages and pointing to the deleted file will be broken, and thus not
		available anymore. Because of that be careful when using this feature.</p>
</body>
</html>
