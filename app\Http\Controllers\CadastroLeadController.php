<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Requests\CadastroLead;
use App\Services\EnviaEmailsCadastroService;
use App\Cadastros;

use Auth;

class CadastroLeadController extends Controller
{
    /**
     * Update the profile for the given user.
     *
     * @param  int  $id
     * @return redirect()
     */
    public function save(CadastroLead $request )
    {
        $input = $request->all();
        $recap = 'g-recaptcha-response';

        $resposta = file_get_contents("https://www.google.com/recaptcha/api/siteverify?secret=".env('RECAPTCHA_SECRET')."&response=".@$request->$recap."&remoteip=".$_SERVER['REMOTE_ADDR']);
        $resposta = json_decode($resposta);

        if (!$resposta->success) {
            return response()->json([
                'status' => 'false'
            ], 503);
        }

        $tipo = $input['lead_tipo_pessoa'] === 'fisica' ? 1 : 2;

        $cadastro = new Cadastros();
        $cadastro->data_cadastro = date('Y-m-d');

        $cadastro->pessoa     = $input['lead_tipo_pessoa'] === 'fisica' ? 1 : 2;
        if ($cadastro->pessoa == 1) {
            $cadastro->nome       = $input['nome-lead'];
        }
        if ($cadastro->pessoa == 2) {
            $cadastro->razao_social    = $input['nome-lead'];
        }
        $cadastro->email           = strtolower($input['email-lead']);

        $cadastro->uso_proprio = @$input['uso_proprio'] === 't' ? true : false;
        $cadastro->investimento = @$input['investimento'] === 't' ? true : false;
        $cadastro->regiao_interesse  = $input['regiao_interesse'] ?? 'NA';
        $cadastro->lead_faixa_valor  = (int) $input['lead_faixa_valor'];
        $cadastro->lead_tipo_imovel  = $input['lead_tipo_imovel'] ?? '0';
        $cadastro->estado          = $input['lead_uf'] ?? 'NA';
        $senha                     = $input['password'];
        $cadastro->changed_password = 1;
        $cadastro->senha           = \Hash::make($senha);
        $cadastro->completar_cadastro = 1;
        if ($cadastro->save()) {
            EnviaEmailsCadastroService::enviaEmails($cadastro, $senha);
            return response()->json([
                'status' => true
            ]);
        }

        return response()->json([
            'status' => false
        ], 500);

    }

}
