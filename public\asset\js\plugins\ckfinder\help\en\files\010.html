<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>CKFinder User's Guide</title>
	<link href="../../files/other/help.css" type="text/css" rel="stylesheet" />
	<script type="text/javascript" src="../../files/other/help.js"></script>
	<meta name="robots" content="noindex, nofollow" />
</head>
<body>
	<h1>
		Status Bar</h1>
	<p>
		The <strong>Status Bar</strong> is the section at the bottom of the CKFinder
		interface that displays information about the selected file, the total number
		of files in the folder, etc.</p>
	<p>
		If a file is selected in CKFinder, the <strong>Status Bar</strong> will display
		detailed information about that file, including the file name, its size, and the
		data of its last modification. For example:</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_status_file.png" width="187" height="39" alt="CKFinder Status Bar with a file selected" />&nbsp;</p>
	<p>
		If no files are selected, the <strong>Status Bar</strong> will instead display the
		total number of files in the current folder. For example:</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_status_folder.png" width="187" height="39" alt="CKFinder Status Bar with no files selected" />&nbsp;</p>
	<p>
		If the folder is empty, the <strong>Status Bar</strong> will display an appropriate
		message. For example:</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_status_empty.png" width="187" height="39" alt="CKFinder Status Bar for an empty folder" />&nbsp;</p>
</body>
</html>
