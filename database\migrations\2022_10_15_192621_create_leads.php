<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLeads extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('leads', function (Blueprint $table) {
            $table->id();
            $table->integer('idlote');
            $table->string('nome', 80);
            $table->string('email', 80);
            $table->string('telefone', 20);
            $table->date('data_nascimento')->nullable();
            $table->decimal('renda_bruta', 10, 2)->nullable();
            $table->string('forma_pagamento', 1)->nullable();
            $table->boolean('contato_whatsapp')->nullable();
            $table->boolean('contato_telefone')->nullable();
            $table->boolean('contato_email')->nullable();
            $table->text('mensagem')->nullable();
            $table->timestamps();
            $table->index(['nome', 'email', 'telefone']);
            $table->index(['data_nascimento']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('leads');
    }
}
