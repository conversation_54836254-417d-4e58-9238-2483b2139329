<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title><PERSON><PERSON><PERSON><PERSON> k<PERSON>je</title><meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<link href="../../files/other/help.css" type="text/css" rel="stylesheet" />
	<script type="text/javascript" src="../../files/other/help.js"></script>
	<meta name="robots" content="noindex, nofollow" />
</head>
<body>
	<h1>
		Pikavalik<PERSON>
	</h1>
	<p>
		"Pikavalikko" koostuu napeista (valikkorivit), j<PERSON>la käynnistetään pikavalikon
		kohteeseen liittyviä toimintoja. Näkyvät toiminnot vaihtuvat valitun kohteen
		mukaisesti. </p>
	<h2>
		Käytettävät valikot</h2>
	<p>
		Seuraavat valikot löydät vakio-CKFinder-asennuksesta.
	</p>
	<h3>
		Kansion pikavalikko</h3>
	<p>
		Valikko ilmestyy, kun napsautat kansiota <a href="003.html">hakemistoruudussa</a>
		hiiren oikealla näppäimellä:</p>
	<p style="text-align: center">
		<img src="images/004.gif" />&nbsp;</p>
	<h3>
		Tiedoston pikavalikko</h3>
	<p>
		Valikko ilmestyy, kun napsautat tiedostoa <a href="004.html">tiedostoruudussa</a>
		hiiren oikealla näppäimellä:</p>
	<p style="text-align: center">
		<img src="images/008.gif" />&nbsp;</p>
	<h3>
		Pikavalikko tyhjälle valinnalle</h3>
	<p>
		Valikko ilmestyy, kun napsautat <a href="004.html">tiedostoruudussa</a>, mutta
		tiedostonimen vieressä (taustaa) hiiren oikealla näppäimellä:</p>
	<p style="text-align: center">
		<img src="images/020.gif" /></p>
</body>
</html>
