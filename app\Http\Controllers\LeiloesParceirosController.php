<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Leiloes;
use App\Http\Requests\CadastroLeiloesParceiroRequest;
use Auth;
use Illuminate\Support\Facades\Log;

class LeiloesParceirosController extends Controller
{
    private $view = 'admin.leiloes.index-parceiros';
    /**
     * List all partners auctions
     *
     * @return view()
     */
    public function index()
    {
        $vet = Leiloes::where('parceiro', true)->get();
        return view($this->view, ['cads' => $vet]);
    }

    /**
     * Update or create a partiner auction
     *
     * @param  int  $id
     * @return redirect()
     */
    public function save(CadastroLeiloesParceiroRequest $request)
    {
        $input = $request->all();
       
        $cadastro = new Leiloes();
        $input['codigo'] = (int) $input['codigo'];
        $cadastro->data_cadastro = date('Y-m-d') ;
        if ($input['codigo'] > 0) {
            $cadastro = Leiloes::findOrFail($input['codigo']);
            unset($cadastro->data_cadastro);
        }
        $cadastro->tipo     = $input['tipo'];
        $input['modalidade'] = 1;
        $cadastro->modalidade   = $input['modalidade'] == '' ? 1 : $input['modalidade'];
        $cadastro->modelo       = 1;
        $cadastro->idleiloeiro  = 3;
        $cadastro->habilitacao   = 1;
        $cadastro->titulo  = $input['titulo'];
        $cadastro->subtitulo = $input['subtitulo'];
        $cadastro->suspender = (int) $input['suspender'];
        
        $cadastro->numero            = '';
        $cadastro->publicacao    = @$input['publicacao'];
        $cadastro->idcomitente      = 0;
        $cadastro->leilao_data_tipo      = (int) $input['leilao_data_tipo'];
        $input['leilao_data_inicial'] = trim ($input['leilao_data_inicial']);
        $input['leilao_data_final'] = trim ($input['leilao_data_final']);
        
        if ($input['leilao_data_inicial'] == '') {
            return redirect()->back()->with('error', 'Data inicial não foi informada.');
        }

        if ($input['leilao_data_final'] == '') {
            return redirect()->back()->with('error', 'Data final não foi informada.');
        }

        if (!checkValidDate($input['leilao_data_inicial'])) {
            return redirect()->back()->with('error', 'Data inicial do primeiro período não é válida [' . $input['leilao_data_inicial'] . ']. Verifique e tente novamente.');
        }

        if (!checkValidDate($input['leilao_data_final'])) {
            return redirect()->back()->with('error', 'Data final do primeiro período não é válida [' . $input['leilao_data_final'] . ']. Verifique e tente novamente.');
        }

        $timeStampDatainicial = \Carbon\Carbon::createFromFormat('d/m/Y', $input['leilao_data_inicial'])->timestamp;

        $timeStampDatafinal = \Carbon\Carbon::createFromFormat('d/m/Y', $input['leilao_data_final'])->timestamp;
        $timeStampDatainicial2 = \Carbon\Carbon::createFromFormat('d/m/Y', $input['leilao2_data_inicial'] ?? $input['leilao_data_inicial'] )->timestamp;
        $timeStampDatafinal2 = \Carbon\Carbon::createFromFormat('d/m/Y', $input['leilao2_data_final'] ?? $input['leilao_data_final'])->timestamp;
         
        $cadastro->leilao_data_inicial   = date('Y-m-d', $timeStampDatainicial);
        $cadastro->leilao_hora_inicial   = $input['leilao_hora_inicial'];

        $cadastro->leilao_data_final    = date('Y-m-d', $timeStampDatafinal);
        $cadastro->leilao_hora_final    = $input['leilao_hora_final'];
        $cadastro->leilao2_data_tipo   = $input['leilao_data_tipo'];
        $cadastro->leilao2_data_inicial   = date('Y-m-d', $timeStampDatainicial2);
        $cadastro->leilao2_hora_inicial   = $input['leilao2_hora_inicial'] ?? $input['leilao_hora_inicial'];
        $cadastro->leilao2_data_final     = date('Y-m-d', $timeStampDatafinal2);
        $cadastro->leilao2_hora_final     = $input['leilao2_hora_final'] ?? $input['leilao_hora_final'];
        $cadastro->responsavel           = '';
        $cadastro->endereco            = '';

        $cadastro->condicao            = trim($input['condicao']);
        $cadastro->desconto            = trim($input['desconto']);

        $cadastro->cidade           = '';
        $cadastro->visitacao        = '';
        $cadastro->restrito         = 2;
        $cadastro->usar_cronometro  = 1;
        
        if ($request->jornal) {
            $cadastro->jornal       = md5($request->jornal->getClientOriginalName() . time() . $cadastro->titulo . $input['codigo']);
            $request->jornal->storeAs('documentos/leilao', $cadastro->jornal);
        }
        if ($request->edital) {
            $edital = $request->edital->getClientOriginalName();
            $ext = $request->edital->getClientOriginalExtension();
            $edital = str_replace([' ', '/', '\\', '(', ')', '=', '+', '_'], '-', $edital);
            $edital = str_replace('.'.$ext, '', $edital);
            $edital = $edital . '-' . md5($edital.time().$input['codigo']) . '.' . $ext;

            $cadastro->edital    = $edital;
            $request->edital->storeAs('documentos/leilao', $cadastro->edital);
        }
        if ($request->logo) {
            $cadastro->logo        = $request->logo->getClientOriginalName();
            $request->logo->storeAs('storage/imagens', $cadastro->logo);
        }
        if ($request->destaque) {
            $imagem  = str_replace(" ", "-", $request->destaque->getClientOriginalName());
            $ext = explode('.', $imagem);
            $ext = $ext[count($ext) - 1];
            $imagem =  md5($imagem . $input['titulo']) . '.' . $ext;
            $cadastro->destaque     = $imagem;
            $request->destaque->storeAs('storage/imagens', $cadastro->destaque);
            
            $request->destaque->storeAs('public/imagens/thumb', $cadastro->destaque, 'local');
            $imgThumb = storage_path('app/public') . '/imagens/thumb/'.$cadastro->destaque;
            
            redimencionaImagem($imgThumb, 250, 166, 'storage/imagens/thumb/' . $cadastro->destaque);
        }
        $cadastro->imagem_360 = '';
        if ($request->imagem_360) {
            $cadastro->imagem_360   = $request->imagem_360->getClientOriginalName();
            $request->imagem_360->storeAs('public/imagens', $cadastro->imagem_360);
        }
        $cadastro->youtube      = $input['youtube'];
        $cadastro->condicoes    = $input['condicoes'];
        $cadastro->regras       = $input['regras'];
        $cadastro->desconto_p   = (int) $input['desconto_p'];
        
        //status futuro
        $timeStampDatainicial = strtotime($cadastro->leilao_data_inicial . ' ' . $cadastro->leilao_hora_inicial);
        
        $cadastro->encerrado    = (int) $input['encerrado'];
        $cadastro->parceiro = true;
        $cadastro->url_parceiro = $input['url_parceiro'];

        $cadastro->save();

        $loteAbertura = explode('-', $cadastro->leilao_data_inicial);
        $loteAbertura = explode('-', $cadastro->leilao_data_inicial);
        $loteAbertura = $loteAbertura[2] . '/' . $loteAbertura[1] . '/' . $loteAbertura[0] . ' ' . substr($cadastro->leilao_hora_inicial, 0, 5);
        
        $loteFechamento = explode('-', $cadastro->leilao2_data_final);
        $loteFechamento = explode('-', $cadastro->leilao2_data_final);
        $loteFechamento = $loteFechamento[2] . '/' . $loteFechamento[1] . '/' . $loteFechamento[0] . ' ' . substr($cadastro->leilao2_hora_final, 0, 5);

        $minEncerramentoProximosLotes = $cadastro->delay_encerramento_lotes * 60;

        //atualiza os lotes 

        $lotes = \App\Lotes::where('idleilao','=', $cadastro->codigo)->orderBy('codigo', 'asc')->get();

        $primeiroLote = true;

        foreach($lotes as $lote) {
            $strTimeDataFinal1 = strtotime($cadastro->leilao_data_final . ' ' . $cadastro->leilao_hora_final);
            $strTimeDataFinal2 = strtotime($cadastro->leilao2_data_final . ' ' . $cadastro->leilao2_hora_final);

            $lote2DataFinal = $cadastro->leilao2_data_final;
            $lote2HoraFinal = $cadastro->leilao2_hora_final;
            $loteDataFinal = $cadastro->leilao_data_final;
            $loteHoraFinal = $cadastro->leilao_hora_final;

            if ($primeiroLote == false) {
                $strTimeDataFinal1 = $strTimeDataFinal1 + $minEncerramentoProximosLotes;
                $strTimeDataFinal2 = $strTimeDataFinal2 + $minEncerramentoProximosLotes;
                $loteDataFinal = date('Y-m-d', $strTimeDataFinal1);
                $loteHoraFinal = date('H:i:s', $strTimeDataFinal1);

                $lote2DataFinal = date('Y-m-d', $strTimeDataFinal2);
                $lote2HoraFinal = date('H:i:s', $strTimeDataFinal2);
                $loteFechamento = date('d/m/Y H:i', $strTimeDataFinal2);

                $minEncerramentoProximosLotes = $minEncerramentoProximosLotes + ($cadastro->delay_encerramento_lotes * 60);
            }
            $primeiroLote = false;

            $lote->encerrado = $cadastro->encerrado;
            $lote->abertura = $loteAbertura;
            $lote->fechamento = $loteFechamento;
            $lote->leilao2_data_final = $lote2DataFinal;
            $lote->leilao2_hora_final = $lote2HoraFinal;
            $lote->leilao_data_final = $loteDataFinal;
            $lote->leilao_hora_final = $loteHoraFinal;

            $lote->leilao2_data_inicial = $cadastro->leilao2_data_inicial;
            $lote->leilao2_hora_inicial = $cadastro->leilao2_hora_inicial;
            $lote->leilao_data_inicial = $cadastro->leilao_data_inicial;
            $lote->leilao_hora_inicial = $cadastro->leilao_hora_inicial;
            $lote->save();
        }

        \App\Lotes::where('idleilao','=', $cadastro->codigo)
                   ->update([
                       'encerrado' => $cadastro->encerrado, 
                       'abertura' => $loteAbertura, 
                       'fechamento' => $loteFechamento]);
        
        return redirect('/leiloes/parceiros/cadastro')->with('success', 'Operação concluída.');
    }

    /**
     * Delete a partner auction
     *
     * @param  int  $id
     * @return redirect()
     */
    public function delete($id)
    {
        $id = (int) $id;
        $user = Auth::guard('admin')->user();
        $cadastro = Leiloes::where('codigo', $id)->where('parceiro', true)->firstOrFail();
        $titulo = $cadastro->titulo;
        $cadastro->delete();

        \App\Lotes::where('idleilao', $id)->delete();
        Log::channel('leiloes')->info('Leilão ' . $id . ' [' . $titulo .'] excluído por ' . $user->nome . ' [' . $user->email . ']');
        
        return redirect('leiloes/parceiros/cadastro')->with('success', 'Leilão ' .$titulo .' excluído com sucesso.');
    }

    /**
     * Show the profile for the given user.
     *
     * @param  int  $id
     * @return View
     */
    public function get($id)
    {
        $id = (int) $id;
        $cadastros = Leiloes::whereIn('encerrado', [1, 7])->where('parceiro', true)->get();
        $leilao = Leiloes::where('codigo', $id)->where('parceiro', true)->firstOrFail()->toArray();
        return view($this->view, ['cads' => $cadastros, 'vet' => $leilao]);
    }
}
