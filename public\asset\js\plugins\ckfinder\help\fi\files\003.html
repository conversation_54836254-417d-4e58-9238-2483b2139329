<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>CK<PERSON><PERSON> k<PERSON>je</title><meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<link href="../../files/other/help.css" type="text/css" rel="stylesheet" />
	<script type="text/javascript" src="../../files/other/help.js"></script>
	<meta name="robots" content="noindex, nofollow" />
</head>
<body>
	<h1>
		Kansioruutu
	</h1>
	<p>
		Kansioiden muodostama puurakenne, jossa voidaan liikkua.
		Kansioiden avulla järjestellään tiedostoja.</p>
	<p>
		<PERSON><PERSON><PERSON><PERSON> kutsuta<PERSON> puun<PERSON>, koska kansiot on esitetty hierarkkisesti kuten puun oksat.
		Yleisilme vastaa modernien graafisten käyttöjärjestelmien tiedostoselainnäkymää.</p>
	<h2>
		Perustoiminnat
	</h2>
	<h3>
		Kansion avaaminen
	</h3>
	<p>
		Avataksesi kansion, eli näyttääksesi alikansiot, napsauta kansion edessä olevaa plusmerkkiä
		(<img src="images/002.gif" height="9" width="9" />). Jollei plusmerkkiä näy, kansio ei sisällä alikansioita.
	</p>
	<p>
		Katso "Kutsusta lataaminen" alempana tällä sivulla ymmärtääksesi, kuinka kansioiden lataaminen toimii.
	</p>
	<h3>
		Kansion sulkeminen
	</h3>
	<p>
		Paina kansion edessä näkyvää miinusmerkkiä sulkeaksesi kansion
		(<img src="images/003.gif" height="9" width="9" />).
	</p>
	<h3>
		Kansion valitseminen
	</h3>
	<p>
		Valitaksesi kansion, napsauta kansionimeä tai -kuvaketta. Valitun kansion taustaväri muuttuu.
	</p>
	<h2>
		Edistyneemmät toiminnot
	</h2>
	<p>
		Kansion edityneempiin toimintoihin pääset "<a href="012.html">pikavalikosta</a>".
		Tarjolla on seuraavat valinnat:
	</p>
	<p style="text-align: center">
		<img src="images/004.gif" width="104" height="75" />&nbsp;</p>
	<p>
		<span class="info">Huom:</span> Jotkin pikavalikon valinnat voivat olla kytkettynä pois,
		mikäli pääkäyttäjä on näin valinnut.
	</p>
	<div>
		<h3>
			Kansioiden luominen
		</h3>
	</div>
	<p>
		Luodaksesi alikansion, napsauta "Uusi alikansio" -valintaa pikavalikossa.
		Valintaruutu ilmestyy kysyen lisättävän kansion nimeä. Kirjoita ja vahvista nimi.
	</p>
	<p>
		Kansio- ja tiedostonimissä ei voi käyttää kaikkia merkkejä. Tämä on
		käyttöjärjestelmäkohtaista. Esim. :
		<strong>\</strong> <strong>/</strong> <strong>:</strong> <strong>*</strong>
		<strong>?</strong> <strong>&quot;</strong> <strong>&lt;</strong>
		<strong>&gt;</strong> <strong>|</strong></p>
	</p>
	<div>
		<h3>
			Kansion uudelleennimeäminen
		</h3>
	</div>
	<p>
		Uudelleennimetäksesi kansion, napsauta "Uudelleennimeä"-valintaa pikavalikossa.
		Ilmestyy valintaikkuna, jossa näkyy kansion nykyinen nimi. Kirjoita uusi nimi
		ja vahvista valinta.
	</p>
	<p>
		Kansio- ja tiedostonimissä ei voi käyttää kaikkia merkkejä. Tämä on
		käyttöjärjestelmäkohtaista. Esim. :
		<strong>\</strong> <strong>/</strong> <strong>:</strong> <strong>*</strong>
		<strong>?</strong> <strong>&quot;</strong> <strong>&lt;</strong>
		<strong>&gt;</strong> <strong>|</strong></p>
	<p>
		<span class="warning">Huom:</span> Kun uudelleennimeät kansion, linkin tai mediatiedoston,
		jota käytetään jollain toisellakin sivulla, toisen sivun linkit rikkoontuvat. Ole siis
		varovainen toimenpiteen suhteen.</p>
	<div>
		<h3>
			Kansion poistaminen
		</h3>
	</div>
	<p>
		Poistaaksesi kansion sisältöineen, napsauta "Poista" pikavalikossa. Näkyviin ilmestyy
		ikkuna, jossa kysytään varmistusta toimenpiteelle.
	</p>
	<p>
		<span class="warning">Huom:</span> Kun poistat kansion, linkin tai mediatiedoston,
		jota käytetään jollain toisellakin sivulla, toisen sivun linkit rikkoontuvat. Ole siis
		varovainen toimenpiteen suhteen.</p>
	<div>
		<h2>
			"Kutsusta lataaminen"
		</h2>
	</div>
	<p>
		CKFinderissa kansioiden sisältö ladataan "kutsusta" toisin kuin paikallisia
		kansioita selattaessa. Ts. aluksi ladataan vain puurakenteen näkyvät kansiot,
		ja vasta avattaessa kansiota ladataan alikansiot ja tiedostot näkyviin. Tämä
		on tavallista nettisovellusten suhteen.
	</p>
	<p>
		Kansioiden latautumisen havaitset ilmoituksesta "Lataan...":
	</p>
	<p style="text-align: center">
		<img src="images/005.gif" width="150" height="78" />&nbsp;</p>
	<p>
		Ilmoitus katoaa automaattisesti kansion lataannuttua. Ensimmäisen latauskerran
		jälkeen ilmoituksen ei pitäisi uudelleen ilmestyä samalle kansiolle.
	</p>
</body>
</html>
