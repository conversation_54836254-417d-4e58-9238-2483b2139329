<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;
use Auth;

class Portals extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => 'required|max:50',
            'url' => 'required|max:250|url',
            'database' => 'required|max:25',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required' => 'O nome do portal é obrigatório',
            'name.max' => 'O nome do portal deve conter, no máximo, :max caracteres',
            'database.max' => 'O nome do banco de dados deve conter, no máximo, :max caracteres',
            'database.required' => 'O nome do banco de dados é obrigatório',
            'url.required' => 'O endereço da web é obrigatório',
            'url.max' => 'O endereço da web, no máximo, :max caracteres',
            'url.url' => 'O endereço da web informado não é válido'
        ];
    }
}
