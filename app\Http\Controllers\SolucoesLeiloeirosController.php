<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Mail;
use App\Mail\EmailGenerico;
use App\Http\Requests\EmailSolucoesLeiloeiros;

class SolucoesLeiloeirosController extends Controller
{
    public function index()
    {
        return view('site.solucoes-para-leiloeiros');
    }

    public function enviaProspect(EmailSolucoesLeiloeiros $request)
    {
        $request->validated();

        $recap = 'g-recaptcha-response';
        $resposta = file_get_contents("https://www.google.com/recaptcha/api/siteverify?secret=".env('RECAPTCHA_SECRET')."&response=".$request->$recap."&remoteip=".$_SERVER['REMOTE_ADDR']);
        $resposta = json_decode($resposta);

        if (!$resposta->success) {
            return response()->json([
                'status' => 'false'
            ], 503);
        }
        $array = [];
        $array['nome'] = $request->nome;
        $array['email'] = $request->email;
        $array['leiloeiro'] = $request->leiloleiro;
        $array['whatsapp'] = $request->whatsapp;
        $array['assunto'] = 'Possível leiloeiro interessado';
        
        $mensagem = '<p>Temos um interessado no gerenciamento de leilões:</p>';
        $mensagem .= '<table>
                    <tr>
                    <td><strong>Nome:</strong> '.$array['nome'].'</td></tr>
                    <tr><td><strong>E-mail:</strong> '.$array['email'].'</td></tr>
                    <tr><td><strong>Telefone:</strong> '.$array['whatsapp'].'</td></tr>
                    <tr><td><strong>É leiloeiro:</strong> '.$array['leiloeiro'].'</td></tr>
                    </table>';
        Mail::to('<EMAIL>')
        ->cc(['<EMAIL>', '<EMAIL>'])
            ->queue(new EmailGenerico('Possível leiloeiro interessado', $mensagem));
        return response()->json([
            'status' => 'true'
        ]);
    }
    
}
