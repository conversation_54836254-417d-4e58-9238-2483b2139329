<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddLeadColumnsToCadastrosTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('cadastros', function (Blueprint $table) {
            $table->string('lead_tipo_imovel', 2);
            $table->string('regiao_interesse', 80);
            $table->smallInteger('lead_faixa_valor');
            $table->boolean('investimento');
            $table->boolean('uso_proprio');
            $table->smallInteger('completar_cadastro');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('cadastros', function (Blueprint $table) {
            $table->dropColumn(['lead_tipo_imovel', 'lead_faixa_valor', 'investimento', 'uso_proprio', 'regiao_interesse']);
        });
    }
}
