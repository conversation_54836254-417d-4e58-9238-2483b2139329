<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>CKFinder User's Guide</title>
	<link href="../../files/other/help.css" type="text/css" rel="stylesheet" />
	<script type="text/javascript" src="../../files/other/help.js"></script>
	<meta name="robots" content="noindex, nofollow" />
</head>
<body>
	<h1>
		Toolbar
	</h1>
	<p>
		The <strong>Toolbar</strong> is a dedicated section at the top of the CKFinder interface. It contains
		a series of menu buttons that can be clicked in order to give you access to various file browser
		features.</p>
	<p>
		This is what the standard CKFinder toolbar looks like:</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_toolbar.png" width="253" height="38" alt="CKFinder Toolbar" />&nbsp;</p>
	<h2>Using the Toolbar</h2>
	<p>In order to perform an operation assigned to a button, click the button once. In most cases it will
		either immediately perform some predefined action or open a drop-down panel with further configuration
		options for a feature.</p>
	<p>Remember that the toolbar can also be used with your keyboard. To enter the toolbar, use the
		<em>Alt+F10</em> keyboard shortcut. To move to the next or previous button, use the <em>Right Arrow</em>
		and <em>Left Arrow</em> keys, respectively. To activate a selected toolbar button, press <em>Enter</em>
		or <em>Space</em>.</p>
	<h2>
		Toolbar Buttons</h2>
	<p>
		The following is the list of buttons available in the standard toolbar:</p>
	<ul>
		<li><strong><a href="006.html">Upload</a></strong> &ndash; opens the <strong>Upload Pane</strong>
			that can be used to add new files to the current folder.</li>
		<li><strong><a href="007.html">Refresh</a></strong> &ndash; reloads the list of files in the
			<strong><a href="004.html">Files Pane</a></strong>.</li>
		<li><strong><a href="008.html">Settings</a></strong> &ndash; opens the <strong>Settings Pane</strong>
			where you can configure and personalize CKFinder.</li>
		<li><strong><a href="009.html">Help</a></strong> &ndash; opens this User's Guide.</li>
	</ul>
</body>
</html>
