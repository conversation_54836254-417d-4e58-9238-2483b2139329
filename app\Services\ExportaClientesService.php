<?php

namespace App\Services;

use App\Cadastros;

class ExportaClientesService
{
    public function exportaClientes($termo = '')
    {
        $dados = $this->cadastros($termo);
        $csv = "Clientes;;;\r\n";
        $csv  .= "Id;Nome;CPF;CNPJ;Email;Apelido;Situação;Data de Cadastro;Como Chegou\r\n";
        $comos = ["Folheto impresso", "Instagram", "Facebook", "Google", "Outros", "Linkedin", "Imovelweb", "VivaReal", "Zap Imóveis", "OLX", "Publicjud", "Moving Imóveis"];

        foreach ($dados as $dado) {
            $id = $dado->codigo;
            $nome = trim($dado->nome);
            $email = $dado->email;
            $apelido = $dado->apelido;
            $situacao = '';
            $dataCadastro = $this->formataData($dado->data_cadastro);
            $dataCadastro = substr($dataCadastro, 0, 10);
            $cpf = trim($dado->cpf);
            $cnpj = '';
            switch ($dado->status) {
                case '1':
                    $situacao = 'Liberado p/ leilões';
                    break;
                case 2:
                    $situacao = 'Bloqueado';
                    break;
                default:
                    $situacao = 'Liberado p/ documentos';
                    break;
            }
            if ($nome == '') {
                $nome = $dado->razao_social;
                $cnpj = trim($dado->cnpj);
            }
            $como_chegou = $dado->como_chegou != 5 ? $comos[$dado->como_chegou - 1] : $dado->desc_como_chegou;
            $como_chegou = $como_chegou == '' ? 'Outros' : $como_chegou;
            $csv  .= "$id;$nome;$cpf;$cnpj;$email;$apelido;$situacao;$dataCadastro;$como_chegou\r\n";
        }

        $dir = __DIR__ . '/../../tmp';
        if (!is_dir($dir)) {
            mkdir($dir);
        }

        $file = 'clientes.csv';
        $fileName = $dir . '/' . $file;
        if (file_exists($fileName)) {
            unlink($fileName);
        }
        $arq = fopen($fileName, 'a+');
        fwrite($arq, $csv);
        fclose($arq);
        return $file;
    }

    private function cadastros($termo = '')
    {
        return Cadastros::select(
            'codigo',
            'nome',
            'razao_social',
            'cpf',
            'cnpj',
            'pessoa',
            'status',
            'como_chegou',
            'desc_como_chegou',
            'last_login',
            'apelido',
            'email',
            'data_cadastro'
        )
            ->where(function ($query) use ($termo) {
                $data = trim($termo);
                if ($data) {
                    $id = (int) $data;
                    $query->where('nome', 'like', '%' . $data . '%')
                        ->orWhere('codigo', '=', $id)
                        ->orWhere('razao_social', 'like', '%' . $data . '%')
                        ->orWhere('cpf', 'like', '%' . $data . '%')
                        ->orWhere('cnpj', 'like', '%' . $data . '%')
                        ->orWhere('email', 'like', '%' . $data . '%');
                }
            })
            ->orderBy('codigo', 'desc')->get();
    }

    private function formataData($data, $hora = true)
    {
        if (!$hora) {
            return \Carbon\Carbon::createFromFormat('Y-m-d', $data)->format('d/m/Y');
        }
        return \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $data)->format('d/m/Y H:i:s');
    }
}
