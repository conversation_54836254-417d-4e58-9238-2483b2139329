<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;
use Auth;

class CadastroLead extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'lead_tipo_pessoa' => 'required',
            'nome-lead' => 'required|min:5|max:20',
            'regiao_interesse' => 'max:80',
            'email-lead' => 'required|email:rfc|unique:mysql_2.cadastros,email,' . (\Auth::user()->codigo  ?? 0).',codigo',
            'password' => 'required|min:6|confirmed',
            'password_confirmation' => 'required|min:6'
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'lead_tipo_pessoa.required' => 'Informe se você é pessoa físcia ou jurídica',
            'nome-lead.required' => 'Você deve informar seu nome',
            'nome-lead.min' => 'Seu nome deve conter, pelo menos, :min caracteres',
            'nome-lead.max' => 'Seu nome deve conter, no máximo, :max caracteres',
            'email-lead.unique' => 'O endereço :input já está cadastrado em nossa base de dados.',
            'email-lead.required' => 'O endereço de email deve ser informado',
            'regiao_interesse.max' => 'Informe a região de interesse com, no máximo, :max caracteres',
            'password.required' => 'A senha deve ser informada',
            'password.min' => 'A senha deve conter, no mínimo, 6 caracteres',
            'password.confirmed' => 'Os campos Senha e Confirmar Senha deve conter os mesmos valores',
            'password_confirmation.required' => 'A senha deve conter, no mínimo, 6 caracteres',
            'password_confirmation.min' => 'A senha deve conter, no mínimo, 6 caracteres',
        ];
    }
}
