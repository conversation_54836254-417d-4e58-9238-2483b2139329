<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;

class S3Export extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 's3:export {path} {to}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $source = $this->argument('path');
	    $destination = $this->argument('to');
        if (is_file($source)) {
            return $this->exportFile($source, $destination);
        }
        $dir = scandir($source);

        foreach ($dir as $file) {
            if ($file  == '..' || $file == '.' || $file == 'thumb' || $file == 'leilao') {
                continue;
            }

            $content = File::get($source .'/' . $file);
            Storage::disk('s3')->put($destination . '/' . $file, $content);
        }
        return 0;
    }

    private function exportFile($file, $destination) {
        $content = File::get($file);
        return Storage::disk('s3')->put($destination . '/' . basename($file), $content);
    }
}
