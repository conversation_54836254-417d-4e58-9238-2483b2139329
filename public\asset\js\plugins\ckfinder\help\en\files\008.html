<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>CKFinder User's Guide</title>
	<link href="../../files/other/help.css" type="text/css" rel="stylesheet" />
	<script type="text/javascript" src="../../files/other/help.js"></script>
	<meta name="robots" content="noindex, nofollow" />
</head>
<body>
	<h1>
		Settings Button</h1>
	<p>
		The <strong>Settings</strong> button that is available in the CKFinder
		<strong><a href="005.html">Toolbar</a></strong> opens the
		<strong>Settings Pane</strong> where you can configure and customize
		CKFinder.</p>
	<p>The figure below presents the file browser <strong>Settings Pane</strong>
		that is expanded when you click the toolbar button.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_settings.png" width="580" height="233" alt="CKFinder Settings Pane" />&nbsp;</p>
	<p>
		All settings are saved automatically by using Internet browser "cookies".
		"Cookies" are small files that store private configuration information
		for specific websites on your computer.</p>
	<p>
		In order to close (collapse) the <strong>Settings Pane</strong>, press
		the <strong>Close</strong> button or click the <strong>Settings</strong>
		toolbar button once again.</p>
	<h2>
		CKFinder Configuration Options</h2>
	<p>
		All configuration options pertain to the <strong><a href="004.html">Files Pane</a>
		</strong> and control the way the files are displayed in CKFinder. The
		<strong>Files Pane</strong> will react immediately to changes introduced
		in the <strong>Settings Pane</strong>.</p>
	<h3>
		View</h3>
	<p>
		Sets the view mode in the <strong>Files Pane</strong>:</p>
	<ul>
		<li><strong>Thumbnails</strong> &ndash; this mode will display each file in a
			box (frame). For images a small preview (called a <em>thumbnail</em>) will
			be displayed inside the box. For other files an icon will be available
			instead.</li>
		<li><strong>List</strong> &ndash; this mode will display all files in a list.
			No image previews are available in this mode.</li>
	</ul>
	<h3>
		Display</h3>
	<p>
		Sets the amount of information available in the <strong>Files Pane</strong>.
		The following options can be turned on and off:</p>
	<ul>
		<li><strong>File Name</strong> &ndash; displays the file name, along with its
			extension.</li>
		<li><strong>Date</strong> &ndash; displays the last file modification date.</li>
		<li><strong>File Size</strong> &ndash; displays the file size, in kilobytes.</li>
	</ul>
	<p>If you are using the <strong>Thumbnails</strong> view mode, you can deselect all
		options. In the <strong>List</strong> mode the file name will always be displayed.</p>
	<p>The image below presents various display options as viewed in the
		<strong>Thumbnail</strong> mode.</p>
	<table align="center" cellpadding="0" cellspacing="0">
		<tr>
			<td valign="top" style="padding-left: 10px">
				<img src="../../files/images/CKFinder_file_display_04.png" width="122" height="112" alt="File displayed in Thumbnails mode without file name, size, and modification date" /></td>
			<td valign="top" style="padding-right: 10px; padding-left: 10px">
				<img src="../../files/images/CKFinder_file_display_03.png" width="122" height="128" alt="File displayed in Thumbnails mode with file name only" /></td>
			<td valign="top" style="padding-right: 10px; padding-left: 10px">
				<img src="../../files/images/CKFinder_file_display_02.png" width="122" height="142" alt="File displayed in Thumbnails mode with file name and modification date" /></td>
			<td valign="top" style="padding-right: 10px">
				<img src="../../files/images/CKFinder_file_display_01.png" width="122" height="158" alt="File displayed in Thumbnails mode with file name, size, and modification date" /></td>
		</tr>
	</table>
	<h3>
		Sorting</h3>
	<p>
		Sets the order in which the files will be listed. The following options are available:</p>
	<ul>
		<li><strong>By File Name</strong> &ndash; sorts the files alphabetically according to
			their names.</li>
		<li><strong>By Date</strong> &ndash; sorts the files by the last modification date, with
			newest displayed first.</li>
		<li><strong>By Size</strong> &ndash; sorts the files by their size, with largest displayed
			first.</li>
		<li><strong>By Extension</strong> &ndash; sorts the files first alphabetically by their
			extension, and then alphabetically according to their names.</li>
	</ul>
</body>
</html>
