<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>CK<PERSON><PERSON></title><meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<link href="../../files/other/help.css" type="text/css" rel="stylesheet" />
	<script type="text/javascript" src="../../files/other/help.js"></script>
	<meta name="robots" content="noindex, nofollow" />
</head>
<body>
	<h1>
		Tiedostoruutu
	</h1>
	<p>
		Tiedostoruutu listaa kansiossa sijaitsevat tiedostot.</p>
	<h2>
		Näkymät</h2>
	<p>
		Tiedostoruutu voidaan esittää kahdella eri ta<PERSON>, riippuen CKFinderin asetuksista (katso
		"<a href="008.html">Asetukset</a>"). Ohessa vertailu "<strong>Esikatselukuva</strong>"
		ja "<strong>Luettelo</strong>" -näkymästä samalle kansiolle:</p>
	<p style="text-align: center">
		<img src="images/007.gif" width="404" height="332" />
	</p>
	<p style="text-align: center">
		<img src="images/006.gif" width="404" height="332" />
	</p>
	<h2>
		Perustoiminnot &nbsp;</h2>
	<h3>
		Tiedoston valinta (aktivointi)</h3>
	<p>
		Napsauta tiedostoa valitaksesi sen. Kun osoitin on tiedoston päällä, tiedostoalue
		muuttaa väriään. Valitun tiedoston taustan väri muuttuu, yleensä siniseksi.
	</p>
	<h2>
		Edistyneemmät toiminnot
	</h2>
	<p>
		Tiedoston edistyneempiin toimintoihin pääset "<a href="012.html">pikavalikosta</a>".
		Seuraavat toiminnot ovat valittavissa:
	</p>
	<p style="text-align: center">
		<img height="122" src="images/008.gif" width="82" />
	</p>
	<p>
		<span class="info">Huom:</span> Jotkin pikavalikon valinnat voivat olla kytkettynä pois,
		mikäli pääkäyttäjä on näin valinnut.
	</p>
	<div>
		<h3>
			Tiedoston valitseminen
		</h3>
	</div>
	<p>
		Valitse tiedosto napsauttamalla "Valitse"-toimintoa.</p>
	<h3>
		Tiedoston esikatselu
	</h3>
	<p>
		Napsauta "Näytä" esikatselleksallesi tiedosta selaimessa. Kaikentyyppisiä tiedostoja
		ei voida näyttää selaimessa, mutta kuvat, teksti ja PDF-tiedostot (Adobe Portable Document Format)
		voidaan. Muissa tapauksissa selain kysyy, millä sovelluksella haluat tiedoston avata. </p>
	<div>
		<h3>
			Tiedostojen lataaminen omalle koneelle</h3>
		<p>
			Ladataksesi tiedoston, napsauta "Lataa"-nappia. Selain pyytää esittämään paikallisen kansion,
			johon haluat tiedoston ladata. </p>
		<h3>
			Tiedostojen uudelleennimeäminen
		</h3>
	</div>
	<p>
		Napsauta "Uudelleennimeä"-nappia pikavalikosta antaaksesi tiedostolle uuden nimen.
		Näkyviin ilmestyy ikkuna, joss lukee tiedoston nykyinen nimi. Anna uusi nimi ja kuittaa.
	</p>
	<p>
		Kansio- ja tiedostonimissä ei voi käyttää kaikkia merkkejä. Tämä on
		käyttöjärjestelmäkohtaista. Esim. :
		<strong>\</strong> <strong>/</strong> <strong>:</strong> <strong>*</strong>
		<strong>?</strong> <strong>&quot;</strong> <strong>&lt;</strong>
		<strong>&gt;</strong> <strong>|</strong></p>
	<p>
		<span class="warning">Huom:</span> Kun uudelleennimeät kansion, linkin tai mediatiedoston,
		jota käytetään jollain toisellakin sivulla, toisen sivun linkit rikkoontuvat. Ole siis
		varovainen toimenpiteen suhteen.</p>
	<div>
		<h3>
			Tiedoston poistaminen
		</h3>
	</div>
	<p>
		Poistaaksesi tiedoston, napsauta "Poista" pikavalikossa. Näkyviin ilmestyy
		ikkuna, jossa kysytään varmistusta toimenpiteelle.
	</p>
	<p>
		<span class="warning">Huom:</span> Kun poistat kansion, linkin tai mediatiedoston,
		jota käytetään jollain toisellakin sivulla, toisen sivun linkit rikkoontuvat. Ole siis
		varovainen toimenpiteen suhteen.</p>
</body>
</html>
