<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Cidades;
use App\Lotes;

class CidadesController extends Controller
{
    /**
     * Show the profile for the given user.
     *
     * @param  int  $id
     * @return View
     */
    public function getCities(Request $request)
    {
        $cidades = Cidades::where('idestado', $request->idestado)->get();
        return view('admin.cidades.combo', ['idcidade' => null,  'cidades' => $cidades]);
    }

    /**
     * Pega as cidades disponíveis de acordo com os lotes existentes
     *
     * @param  int  $id
     * @return View
     */
    public function getCitiesLots(Request $request)
    {
        $cidades = \App\Lotes::cidadesDisponiveis($request->idestado);
        return view('admin.cidades.combo', ['idcidade' => null,  'cidades' => $cidades['cidades']->toArray()]);
    }

    /**
     * Pega as cidades disponíveis de acordo com os lotes existentes
     *
     * @param  Request  $request
     * @return View
     */
    public function getNeighborhood(Request $request)
    {
        $estado = $request->idestado;
        $estado = explode("|", $estado);
        $bairros = Lotes::join('leiloes', 'leiloes.codigo', 'lotes.idleilao')
						->select('bairro')->distinct()
						->where('leiloes.suspender', 2)
                        ->whereIn('leiloes.encerrado', [1, 7, 9, 11])
						->where('idestado', (int) $estado[1]);
        $idCidade = trim($request->idcidade);
        if ($idCidade != '') {
            $idCidade = explode("|", $idCidade);
            $idCidade = $idCidade[1];
        }
        if ($idCidade > 0) {
            $bairros = $bairros->where('idcidade', $idCidade);
        }
        $bairros = $bairros->orderBy('bairro');
        $bairros = $bairros->get();
        return view('admin.cidades.combo-bairro', ['bairros' => $bairros]);
    }

}