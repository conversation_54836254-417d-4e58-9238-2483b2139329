<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Lotes;

class AjustaSequenciaLeilaoCaixa extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $lotes = Lotes::where('idleilao', 647)->get();
        foreach ($lotes as $lote) {
            $lote->titulo = str_replace(['(', ')'], '', $lote->titulo);
            $titulo = explode('-', $lote->titulo);
            $titulo = $titulo[0];
            $numeroLote = (int) str_replace('LOTE ', '', $titulo);
            if ($numeroLote == 0) {
                echo $lote->titulo.chr(10);
                die;
            }
            $lote->numero_lote = $numeroLote;
            $lote->save();
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        $lotes = Lotes::where('idleilao', 647)->get();
        foreach ($lotes as $lote) {
            $titulo = explode($lote->titulo, '-');
            $titulo = $titulo[0];
            $numeroLote = (int) str_replace('LOTE ', '', $titulo);
            $lote->numero_lote = null;
            $lote->save();
        }
    }
}
