<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--
 * CKFinder
 * ========
 * http://cksource.com/ckfinder
 * Copyright (C) 2007-2013, CKSource - <PERSON><PERSON>. All rights reserved.
 *
 * The software, this file and its contents are subject to the CKFinder
 * License. Please read the license.txt file before using, installing, copying,
 * modifying or distribute this file or part of its contents. The contents of
 * this file is part of the Source Code of CKFinder.
-->
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>CKFinder - Samples</title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta name="robots" content="noindex, nofollow" />
	<link href="sample.css" rel="stylesheet" type="text/css" />
</head>
<body>
	<h1 class="samples">
		CKFinder Samples Site
	</h1>
	<h2 class="samples">
		Basic Samples (JavaScript)
	</h2>
	<ul class="samples">
		<li><a class="samples" href="standalone.html">Standalone</a><br />The basic integration sample.</li>
		<li><a class="samples" href="standalone_v1.html">Standalone (V1)</a><br />The basic integration sample using the old "V1" API from CKFinder 1.x.</li>
		<li><a class="samples" href="popup.html">Popup</a><br />CKFinder running in a popup.</li>
		<li><a class="samples" href="popups.html">Popups</a><br />CKFinder running in a popup, each instance having its own configuration.</li>
		<li><a class="samples" href="public_api.html">Public API</a><br />CKFinder API usage.</li>
		<li><a class="samples" href="ckeditor.html">CKEditor integration</a><br />CKEditor with CKFinder being used as a file browser.</li>
		<li><a class="samples" href="fckeditor.html">FCKeditor integration</a><br />FCKeditor with CKFinder being used as a file browser.</li>
	</ul>
	<h2 class="samples">
		Basic Samples (PHP)
	</h2>
	<ul class="samples">
		<li><a class="samples" href="php/standalone.php">Standalone</a><br />The basic integration sample.</li>
		<li><a class="samples" href="php/popup.php">Popup</a><br />CKFinder running in a popup.</li>
		<li><a class="samples" href="php/popups.php">Popups</a><br />CKFinder running in a popup, each instance having it's own configuration.</li>
		<li><a class="samples" href="php/ckeditor.php">CKEditor integration</a><br />CKEditor with CKFinder being used as a file browser.</li>
		<li><a class="samples" href="php/fckeditor.php">FCKeditor integration</a><br />FCKeditor with CKFinder being used as a file browser.</li>
	</ul>
	<div id="footer">
		<hr />
		<p>
			CKFinder - Ajax File Manager - <a class="samples" href="http://cksource.com/ckfinder/">http://cksource.com/ckfinder</a>
		</p>
		<p id="copy">
			Copyright &copy; 2003-2013, <a class="samples" href="http://cksource.com/">CKSource</a> - Frederico Knabben. All rights reserved.
		</p>
	</div>
</body>
</html>
