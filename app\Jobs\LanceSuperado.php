<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Lotes;
use Illuminate\Support\Facades\Mail;
use App\Mail\EmailLanceSuperado;
use App\UserSite;
use Illuminate\Support\Facades\Log;
use App\LoteAviso;

class LanceSuperado implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $usuarioDoMaiorLance;
    private $lote;
    private $idLote;
    private $idUsuarioDestinatario;
    private $avisosAtivos;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($idLote, $idUsuario)
    {
        $this->idLote = $idLote;
        $this->idUsuarioDestinatario = $idUsuario;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $lote = Lotes::find($this->idLote);
        $this->lote = $lote;
        $this->usuarioDoMaiorLance = $lote->getLancesComCadastrosAttribute()->first()->idcadastro;

        if ($this->enviaAvisoSuperao()) {
            $usuario = new UserSite();

            $user = $usuario->find($this->idUsuarioDestinatario);
            $nome = $user->nome != '' ? $user->nome : $user->razao_socal;
            $appurl = env('APP_URL');
            $urlLog = $appurl.'/leilao/lote/' . $this->lote->codigo . '/'. \Str::slug($this->lote->titulo);
            $url = '<a href="'.$urlLog.'">ESTE LEILÃO</a>';
            $texto = '<p>Caro(a) ' . $nome . '</p>';
            $texto .= '<p>Seu lance foi superado por outro usuário do site.</p>';
            $texto .= '<p>Para não perder a oportunidade, acesse ' . $url . ' e oferte um novo lance!</p>';
            $texto .= '<p>Atenciosamente,</p>';
            $texto .= 'Balbino Leilões - Responsabilidade Social e Ambiental<br>';
            $texto .= 'Telefone e whatsaap: (11) 4020-1694<br />';
            $texto .= 'Rua 2, JK, n° 264, Cuiabá - MT<br>';
            $texto .= 'CEP: 78068-340<br>';
            $texto .= 'Rua Travessa Oceânica, 171, Cachoeira do Bom Jesus, Florianópolis SC<br>';
            $texto .= 'CEP: 88056-481<br>';
            $texto .= '<EMAIL> - www.balbinoleiloes.com.br';

            Mail::to($user->email)
                ->queue(new EmailLanceSuperado('Seu foi superado por outro usuário', $texto, $this->avisoAtivo));

        }
        return true;

    }

    private function enviaAvisoSuperao() {
        $this->avisoAtivo = LoteAviso::avisoAtivo($this->lote->codigo, $this->idUsuarioDestinatario);

        if ($this->usuarioDoMaiorLance != $this->idUsuarioDestinatario) {
            return true;
        }
        if ($this->avisoAtivo) {
            $this->avisoAtivo->delete();
        }
        return false;
    }


}
