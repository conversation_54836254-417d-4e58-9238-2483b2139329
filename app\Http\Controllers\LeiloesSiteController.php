<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Services\BuscaLeiloesService;

class LeiloesSiteController extends Controller
{
    /**
     * List all profiles.
     *
     * @param  int  $id
     * @return redirect()
     */
    public function index(Request $request, $tipo = 'todos', $categoria = '')
    {
        $buscaLeiloes = BuscaLeiloesService::leiloes($request, $tipo, $categoria);
        
        return view('site.leiloes.leiloes', $buscaLeiloes);
    }
}
