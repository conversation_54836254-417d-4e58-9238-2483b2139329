<?php

namespace App\Utils;

use App\Utils\SendMail;
use App\User;

class Notifications extends SendMail
{
    public function posCad($nome, $email, $senha)
    {
        $text_body = 'Prezado(a) Senhor(a) '. $nome .', seu cadastro foi efetuado com sucesso no site da Balbino Leilões!<br><br>';
        $text_body .= 'Seus dados para acessar ao site são:<br>';
        $text_body .= 'Login: '.$email.'<br>';
        $text_body .= 'Senha: '.$senha.'<br><br>';
        $text_body .= 'Antes de se habilitar para ofertar lances em nossos leilões, você deve fazer seu login em nosso site, clicar no item "minha conta" e anexar os seguintes documentos (tamanho máximo de 2mb por arquivo):<br><br>';
        $text_body .= '- Cópia simples do RG e CPF ou CNH<br>';
        $text_body .= '- Comprovante de residência (qualquer conta de consumo recente)<br><br>';
        $text_body .= 'Após enviar seus documentos você receberá um e-mail de nosso parceiro "clicksign" com um documento denominado "Declarações para Participar em Leilões". Ao clicar em "Ver Documento" você será direcionado para o site da clicksign para assinar digitalmente essa declaração.<br><br>';
        $text_body .= 'Assim que você assinar digitalmente a "Declarações para Participar em Leilões" no site da "clicksign" você estará apto para participar dos nossos leilões.<br><br>';
        $text_body .= 'Atenciosamente<br>';
        $text_body .= 'Balbino Leilões - Responsabilidade Social e Ambiental<br>';
        $text_body .= 'Telefone e whatsaap: (11) 4020-1694<br />';
        $text_body .= 'Rua 2, JK, n° 264, Cuiabá - MT<br>';
        $text_body .= 'CEP: 78068-340<br>';
        $text_body .= 'Rua Travessa Oceânica, 171, Cachoeira do Bom Jesus, Florianópolis SC<br>';
        $text_body .= 'CEP: 88056-481<br>';
        $text_body .= '<EMAIL> - www.balbinoleiloes.com.br';
        $this->view = 'layout.email.blank';


        $this->send($email, ['body' => $text_body], 'Seu cadastro foi efetuado com sucesso');

        $text_body = 'O cliente ' . $nome . ', acaba de se cadastrar no site com o email '.$email.'!<br>';
        $text_body .= 'Acesse o gerenciador do site para visualizar seus dados.';

        $adminEmails = User::select('email')->where('status', '=', '1')
                             ->whereNotNull('email')->get()->toArray();
        $emails = [];
        foreach ($adminEmails  as $key => $value) {
            $emails[] = $value;
        }
        $this->view = 'layout.email.blank';
        $this->send($emails, ['body' => $text_body], 'Novo cadastro realizado');
    }

}