<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--
 * CKFinder
 * ========
 * http://cksource.com/ckfinder
 * Copyright (C) 2007-2013, CKSource - <PERSON><PERSON>. All rights reserved.
 *
 * The software, this file and its contents are subject to the CKFinder
 * License. Please read the license.txt file before using, installing, copying,
 * modifying or distribute this file or part of its contents. The contents of
 * this file is part of the Source Code of CKFinder.
-->
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>CKFinder - Sample - Public API</title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta name="robots" content="noindex, nofollow" />
	<link href="sample.css" rel="stylesheet" type="text/css" />
	<script type="text/javascript" src="../ckfinder.js"></script>
</head>
<body>
	<h1 class="samples">
		CKFinder - Sample - API examples
	</h1>
	<div class="description">
	<p>
		CKFinder contains simple yet powerful <a href="http://docs.cksource.com/ckfinder_2.x_api/symbols/CKFinderAPI.html">API</a> which allows you to extend it with your own features.
	</p>
	<p>
		In this example, please check the <strong>file and folder context menu</strong> and <strong>toolbar in the Basket folder</strong>.
	</p>
	</div>
	<p style="padding-left: 30px; padding-right: 30px;">
		<script type="text/javascript">

			// See http://docs.cksource.com/ckfinder_2.x_api/symbols/CKFinder.html#.addPlugin
			// See http://docs.cksource.com/CKFinder_2.x/Developers_Guide/PHP/Plugins/Writing_JavaScript
			CKFinder.addPlugin('basketTest',
			{
				basketToolbar :
				[
					[
						// UI type have to be in string form.
						'basketTest',
						{
							label : 'Basket Test',
							icon : false,
							onClick : function()
							{
								alert( 'My own function executed after pressing Basket Test.' );
							}
						}
					]
				],
				basketFileContextMenu :
				[
					[
						'basketTest',
						{
							label : 'Basket Test',
							onClick : function()
							{
								alert( 'My own function executed after selecting Basket Test in the context menu.' );
							},
							group : 'file1'
						}
					]
				]
			});

			// You can use the "CKFinder" class to render CKFinder in a page:
			var finder = new CKFinder();
			finder.extraPlugins = 'basketTest';
			finder.basePath = '../';	// The path for the installation of CKFinder (default = "/ckfinder/").
			finder.callback = function( api )
			{
				// Add new sidebar tool space (with layout).
				var toolId = api.addToolPanel();
				api.hideTool( toolId );

				// Add new file context menu option.
				var sampleFileContextMenu =
				{
					label : 'API test',
					command : 'test',
					group : 'file3'
				};
				api.addFileContextMenuOption( sampleFileContextMenu, function( api, file )
				{
					var markup = "<h3>Selected file</h3><p>You've selected file &quot;" + file.folder.getPath() + file.name + "&quot;</p>";

					api.document.getElementById( toolId ).innerHTML = markup;
					api.showTool( toolId );

					api.openMsgDialog( "", "Take a look at the bottom of left sidebar - it contains information about selected file." );
				});

				// Add new folder context menu option.
				var sampleFolderContextMenu =
				{
					label : 'API test',
					command : 'test',
					group : 'folder3'
				};
				api.addFolderContextMenuOption( sampleFolderContextMenu, function( api, folder )
				{
					var markup = "<h3>Selected folder</h3><p>You've selected folder &quot;" + folder.getPath() + "&quot;</p>";

					api.document.getElementById( toolId ).innerHTML = markup;
					api.showTool( toolId );

					api.openMsgDialog( "", "Take a look at the bottom of left sidebar - it contains information about selected folder." );
				});
			};
			finder.create();
		</script>
	</p>
	<div id="footer">
		<hr />
		<p>
			CKFinder - Ajax File Manager - <a class="samples" href="http://cksource.com/ckfinder/">http://cksource.com/ckfinder</a>
		</p>
		<p id="copy">
			Copyright &copy; 2003-2013, <a class="samples" href="http://cksource.com/">CKSource</a> - Frederico Knabben. All rights reserved.
		</p>
	</div>
</body>
</html>
