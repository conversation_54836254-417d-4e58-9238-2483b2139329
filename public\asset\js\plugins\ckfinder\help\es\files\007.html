<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
	"http://www.w3.org/TR/html4/loose.dtd">
<html lang="es">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<title>Guía del usuario de CKFinder</title>
	<link href="../../files/other/help.css" type="text/css" rel="stylesheet">
	<script type="text/javascript" src="../../files/other/help.js"></script>
	<meta name="robots" content="noindex, nofollow" />
</head>
<body>
	<h1>
		Bot&oacute;n Actualizar </h1>
	<p >
		Cuando se trabaja en un espacio compartido como lo es CKFinder, en donde docenas o incluso cientos de usuarios est&aacute;n trabajando en los mismos ficheros, podr&iacute;a suceder que alguno de estos otros usuarios realice cambios en la misma carpeta en la que usted se encuentra trabajando. Para recargar la lista de ficheros, solamente haga click sobre el bot&oacute;n &quot;Actualizar&quot;, esto le presentar&aacute; la lista de ficheros como se encuentra en ese instante.</p>
	<p style="text-align: center">
		<img src="images/011.png" width="288" height="24" alt="botón Actualizar" />&nbsp;</p>
	<p>Por ejemplo, imagine usted que tiene que crear una p&aacute;gina para un nuevo producto de su compa&ntilde;&iacute;a. Usted abre entonces CKFinder para obtener la fotograf&iacute;a del producto para ser incluida en el sitio web de la empresa, pero cuando abre la	carpeta &quot;Productos&quot; usted no puede encontrar esa fotograf&iacute;a. Toma el tel&eacute;fono y llama a &quot;Beth&quot;, diciendo: &quot;&iexcl;Hey Beth, la imagen del producto no se encuentra en CKFinder!&quot;. Beth dice entonces: &quot;Ops... espera un minuto&quot;. Ella abre CKFinder en su ordenador, sube el fichero con la fotograf&iacute;a desde su ordenador y le dice: &quot;Ah&iacute; est&aacute;, solo Actualiza&quot;. Usted entonces d&aacute; click sobre el bot&oacute;n &quot;Actualizar&quot; y &iexcl;voila!, el fichero aparece ah&iacute; para usted. Es por esto que CKFinder es tambi&eacute;n conocido como un programa de colaboraci&oacute;n. </p>
</body>
</html>
