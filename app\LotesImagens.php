<?php

namespace App;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use OwenIt\Auditing\Contracts\Auditable;

class LotesImagens extends Model implements Auditable
{
    use Notifiable;
    use \OwenIt\Auditing\Auditable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'idlote',
        'arquivo',
        'ordem',
    ];

    public function __construct() {
    parent::__construct();
    $middlewares = (\Route::current());

        if ($middlewares) {
            $middlewares = $middlewares->gatherMiddleware();
            if (in_array('auth', $middlewares) == true) {
                if(session()->has('portaldb')) {
                    $this->table = session()->get('portaldb') . '.' . $this->table;
                }
            }
        }
    }

    protected $table = 'lotes_imagens';
    protected $primaryKey = 'codigo';
    public $timestamps = false;
    
}