<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>CKFinder vartotojo instrukcija</title>
	<link href="../../files/other/help.css" type="text/css" rel="stylesheet" />
	<script type="text/javascript" src="../../files/other/help.js"></script>
	<meta name="robots" content="noindex, nofollow" />
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" /></head>
<body>
	<h1>
		Nustatymų mygtukas
</h1>
	<p>"Nustatymų" mygtukas, esantis <a href="005.html">įrankių juostoje</a>, atveria "Nustatymų
", kur galite konfigūruoti CKFinder. Štai pavyzdys:</p>
	<p style="text-align: center">
		<img src="images/013.gif" height="190" width="404" /></p>
	<p>
		Visi nustatymai yra automatiškai išsaugojami naudojant naršyklės "sausainėlius". Sausainėliai
</p>
	<p>
		Jei norite užverti nustatymų skydelį, tiesiog spustelėkite "Uždaryti" mygtuką, arba "Nustatymai"
</p>
	<p>
		Visi parametrai yra susiję su <a href="004.html">Failų skydeliu</a>.
		Jie yra naudojami informacijos vaizdavimui skydelyje kontroliuoti. Failų valdymo skydelis
</p>
	<h3>&nbsp;
</h3>
	<p>
<a href="004.html">Failų Skydelyje</a>:</p>
	<ul>
		<li>"<strong>Miniatiūrų</strong>" režimas kiekvieną failą rodys kaip "dėžutę". Paveiksliukams,
			bus rodomas sumažintas paveiksliukas (vadinama miniatiūra) kuri bus rodoma dėžutėje.
	  Kitiems failams, vietoj to bus rodomas ikonas.</li>
	</ul>
	<ul>
		<li>"<strong>Sąrašo</strong>" režimas atvaizduos visus failus kaip sąrašą, vieną po kito.
	  Šiame režime nebus galima matyti miniatiūrų.</li>
	</ul>
	<h3>
		Rodymas</h3>
	<p>
		Nustato informacijos kiekį failų skydelyje. Štai pateiktas pavyzdys nuo minimalaus
		galimų pasirinkimų iki maksimalaus:
	</p>
	<p style="text-align:center">
		<table align="center" cellpadding="0" cellspacing="0">
			<tr>
				<td valign="top" style="padding-right: 10px">
					<img src="images/014.gif" width="112" height="112" /></td>
				<td valign="top" style="padding-right: 10px; padding-left: 10px">
					<img src="images/015.gif" width="112" height="128" /></td>
				<td valign="top" style="padding-right: 10px; padding-left: 10px">
					<img src="images/016.gif" width="112" height="144" /></td>
				<td valign="top" style="padding-left: 10px">
					<img src="images/017.gif" width="112" height="160" /></td>
			</tr>
		</table>
	</p>
	<h3>
		Rūšiavimas</h3>
	<p>
		Nustato, kokia tvarka yra rūšiuojami failai. Rūšiavimas gali būti nustatomas pagal
		abėcėlę, failo pavadinimą, datą pagal failų naujumą, arba netgi pagal failo apimtį.
	</p>
</body>
</html>
