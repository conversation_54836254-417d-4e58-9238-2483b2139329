<?php

namespace App;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Contracts\Auditable;

class Cadastros extends Model implements Auditable
{
    use Notifiable;
    use SoftDeletes;
    use \OwenIt\Auditing\Auditable;

    protected $connection = 'mysql_2';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'pessoa', 'nome', 'cpf', 'rg', 'filiacao', 'profissao', 'empregador', 'sexo',
        'estado_civil', 'tipo_uniao', 'conjuge', 'c_cpf', 'c_rg', 'razao_social', 'cnpj',
        'insc_estadual', 'nome_fantasia', 'faturamento', 'segmento', 'socio', 's_cpf', 's_rg',
        'email', 'telefone', 'celular', 'cep', 'endereco', 'numero', 'complemento', 'bairro', 'cidade',
        'estado', 'informacoes', 'como_chegou', 'status', 'senha','desc_como_chegou',
        'responsavel_cadastro', 'last_login',
        'lead_tipo_imovel','regiao_interesse','lead_faixa_valor',
        'investimento','uso_proprio','completar_cadastro', 'data_cadastro'
    ];

    protected $table = 'cadastros';
    protected $primaryKey = 'codigo';
    protected $dates = ['deleted_at', 'last_login', 'data_cadastro'];

    public function __construct() {
    parent::__construct();
    $middlewares = (\Route::current());

    if ($middlewares) {
        $middlewares = $middlewares->gatherMiddleware();
        if (in_array('auth', $middlewares) == true) {
            if(session()->has('portaldb')) {
                $this->table = session()->get('portaldb') . '.' . $this->table;
            }
        }
    }
}

    public function docPessoal()
    {
        return $this->hasOne('App\CadastrosDocumentos', 'idcadastro', 'codigo')
               ->whereNotNull('arq_pessoal')->latest('codigo');
    }

    public function docContrato() {
        return $this->hasOne('App\CadastrosDocumentos', 'idcadastro', 'codigo')
               ->whereNotNull('contrato_social')->latest('codigo');
    }

    public function docEndereco()
    {
        return $this->hasOne('App\CadastrosDocumentos', 'idcadastro', 'codigo')
               ->whereNotNull('arq_residencia')->latest('codigo');
    }

    public function habilitados()
    {
        return $this->hasMany('App\CadastrosHabilitados', 'idcadastro', 'codigo')
                ->join('lotes', 'cadastros_habilitados.idlote', '=', 'lotes.codigo')
                ->select('cadastros_habilitados.*', 'lotes.titulo', 'lotes.encerrado', 'lotes.codigo')
                ->orderBy('data_habilitacao', 'desc');
    }
    public function lances()
    {
        return $this->hasMany('App\Lances', 'idcadastro', 'codigo')
                ->join('lotes', 'lances.idlote', '=', 'lotes.codigo')
                ->join('leiloes', 'lotes.idleilao', '=', 'leiloes.codigo')
                ->select('lances.*', 'lotes.titulo', 'leiloes.titulo as leilao_titulo', 'lotes.codigo')
                ->orderBy('data_lance', 'desc');
    }

    public function atendimentos()
    {
        return $this->hasMany('App\ClienteAtendimentos', 'cadastro_id', 'codigo');
    }

    public function arrematacao()
    {
        return $this->hasMany('App\Lances', 'idcadastro', 'codigo')
                ->join('lotes', 'lances.idlote', '=', 'lotes.codigo')
                ->join('leiloes', 'lotes.idleilao', '=', 'leiloes.codigo')
                ->select('lances.*', 'lotes.titulo', 'leiloes.titulo as leilao_titulo', 'lotes.codigo', 'lotes.encerrado')
                ->where('lotes.encerrado', '=', 8)
                ->orderBy('data_lance', 'desc');
    }

    public function historicoContato() {
        return $this->hasMany('App\HistoricoContato', 'idcadastro', 'codigo');
    }

    public function ultimoLanceAutomatico() {
        return $this->hasMany('App\Lances', 'idcadastro', 'codigo')
                ->join('lotes', 'lances.idlote', '=', 'lotes.codigo')
                ->join('leiloes', 'lotes.idleilao', '=', 'leiloes.codigo')
                ->select('lances.*', 'lotes.titulo', 'leiloes.titulo as leilao_titulo', 'lotes.codigo', 'lotes.encerrado')
                ->where('lances.desativado', '!=', 1)
                ->where('lotes.encerrado', '=', 1)
                ->groupBy('idlote')
                ->orderBy('lances.codigo', 'desc');

    }
}
