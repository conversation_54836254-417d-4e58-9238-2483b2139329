<?php

namespace App\Jobs;

use App\Lotes;
use App\Lances;
use App\Leiloes;
use App\UserSite;
use App\Mail\EmailGenerico;
use Illuminate\Bus\Queueable;
use App\Routines\LanceRoutine;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\LanceAutomatico as LanceAutomaticoModel;

class LanceAutomatico implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $lances;
    private $usuario;

    public function __construct(Lances $lances)
    {
        $this->lances = $lances;
    }

    public function handle()
    {
        // Obter o lote com relacionamentos necessários
        $lote = Lotes::with(['leilao', 'estado'])->findOrFail($this->lances->idlote);

        // Definir o timezone com base no estado do leilão
        if ($lote->num_processo) {
            $timezone = getFuso($lote->num_processo);
        } else {
            $timezone = LanceRoutine::getTimezoneByUF($lote->estado->uf);
        }

        // Configurar timezone para todas as operações de data/hora
        date_default_timezone_set($timezone);

        $lancesAutomaticos = LanceAutomaticoModel::where('idlote', $this->lances->idlote)
            ->get();

        if ($lancesAutomaticos->isEmpty()) {
            return true;
        }

        foreach ($lancesAutomaticos as $lanceAutomatico) {
            $leilao = Leiloes::find($lote['leilao']->codigo);

            // Verificar se o usuário já é o atual líder de lances
            $lancesComCadastros = $lote->getLancesComCadastrosAttribute();
            if (!empty($lancesComCadastros) && $lancesComCadastros[0]->idcadastro == $lanceAutomatico->idcadastro) {
                continue;
            }

            $leilaoInfo = new \StdClass();
            $leilaoInfo->leilao = $leilao;
            $leilaoInfo->lote = $lote;
            $leilaoInfo->lance_automatico = 1;
            $leilaoInfo->valorLance = $lancesComCadastros[0]->valor + $lote->incremento;

            $usuario = new UserSite();
            $user = $usuario->find($lanceAutomatico->idcadastro);
            $nome = $user->nome != '' ? $user->nome : $user->razao_social;

            $appurl = env('APP_URL');
            $urlLog = $appurl . '/leilao/lote/' . $this->lances->idlote . '/' . urlTitulo($leilao->titulo);
            $url = '<a href="' . $urlLog . '">ESTE LEILÃO</a>';

            // Verificar se o lance automático ainda é válido
            if ($lanceAutomatico->valor < $leilaoInfo->valorLance) {
                $this->handleLanceSuperado($lanceAutomatico, $user, $nome, $url);
                continue;
            }

            // Executar o lance automático
            $this->executarLanceAutomatico($lanceAutomatico, $leilaoInfo, $lote, $nome, $urlLog);

            // Pequena pausa entre lances automáticos
            sleep(5);
        }
    }

    /**
     * Trata o caso quando o lance automático foi superado
     */
    private function handleLanceSuperado($lanceAutomatico, $user, $nome, $url)
    {
        LanceAutomaticoModel::where('codigo', $lanceAutomatico->codigo)->delete();

        $texto = '<p>Caro(a) ' . $nome . '</p>';
        $texto .= '<p>Seu lance automático foi superado por outro usuário do site.</p>';
        $texto .= '<p>Para não perder a oportunidade, acesse ' . $url . ' e oferte um novo lance!</p>';
        $texto .= '<p>Atenciosamente,</p>';
        $texto .= 'Balbino Leilões - Responsabilidade Social e Ambiental<br>';
        $texto .= 'Telefone e whatsaap: (11) 4020-1694<br />';
        $texto .= 'Rua 2, JK, n° 264, Cuiabá - MT<br>';
        $texto .= 'CEP: 78068-340<br>';
        $texto .= 'Rua Travessa Oceânica, 171, Cachoeira do Bom Jesus, Florianópolis SC<br>';
        $texto .= 'CEP: 88056-481<br>';
        $texto .= '<EMAIL> - www.balbinoleiloes.com.br';

        Mail::to($user->email)
            ->queue(new EmailGenerico('Seu lance automático foi superado por outro usuário', $texto));

        Log::channel('lances_automaticos')->info('Lance Automático não efetuado - ' . $nome . ' teve seu lance superado');
    }

    /**
     * Executa um lance automático
     */
    private function executarLanceAutomatico($lanceAutomatico, $leilaoInfo, $lote, $nome, $urlLog)
    {
        $lanceRoutine = new LanceRoutine();
        $resultado = $lanceRoutine->addLance($leilaoInfo, $this->lances->idlote, $lanceAutomatico->idcadastro);

        if ($resultado) {
            Log::channel('lances_automaticos')->info(
                'Lance Automático executado - ' . $nome . '. Lote: ' . $urlLog
            );
        } else {
            Log::channel('lances_automaticos')->error(
                'Falha ao executar lance automático - ' . $nome . '. Lote: ' . $urlLog
            );
        }
    }
}
