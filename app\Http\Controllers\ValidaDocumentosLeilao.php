<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Storage;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Leiloes;
use App\LotesAnexos;
use App\Lotes;

class ValidaDocumentosLeilao extends Controller
{
    /**
     * Show the profile for the given user.
     *
     * @param  int  $id
     * @return View
     */
    public function getEdital($tipoArquivo = '', $codigo = 0, $arquivo = '')
    {
        $arquivo = $this->getFileOfLeilaoOrLote($tipoArquivo, $arquivo, $codigo);
        
        $nomeDoArquivo = $arquivo;

        return response()->redirectTo(env('CLOUD_FRONT_URL') . '/documentos/leilao/' . $nomeDoArquivo);
    }

    private function getFileOfLeilaoOrLote($tipoArquivo, $arquivo, $codigo) {
        $nomeArquivo = '';
        switch($tipoArquivo) {
            case 'edital':
            case 'jornal':
                $registro = Leiloes::where('suspender', '!=', '1')->where('codigo', $codigo)->firstOrFail();
                $nomeArquivo = $registro->$tipoArquivo;
            break;
            case 'anexos':
                $registro = LotesAnexos::where('idlote', $codigo)->where('arquivo', $arquivo)->firstOrFail();
                $leilao = $registro->lote()->firstOrFail()->leilao()->firstOrFail();
                if ((int) $leilao->suspender == 1) {
                    return abort(404);    
                }
                $nomeArquivo = $registro->arquivo;
            break;
            default:
                return abort(404);
            break;
        }
        return $nomeArquivo;
    }
}