<?php

namespace App;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use OwenIt\Auditing\Contracts\Auditable;

class LancesParcelados extends Model implements Auditable
{
    use Notifiable;
    use \OwenIt\Auditing\Auditable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'idcadastro',
        'idlote',
        'tipo',
        'valor',
        'data_lance',
        'data_cronometro',
        'ind_cronometro',
        'ind_envio',
        'status',
        'obs',
        'desativado',
        'data_desativa',
        'usuario_desativa',
    ];

    protected $table = 'lances_parcelados';
    protected $primaryKey = 'codigo';
    public $timestamps = false;

    public function __construct() {
        parent::__construct();
        $middlewares = (\Route::current());

        if ($middlewares) {
            $middlewares = $middlewares->gatherMiddleware();
            if (in_array('auth', $middlewares) == true) {
                if(session()->has('portaldb')) {
                    $this->table = session()->get('portaldb') . '.' . $this->table;
                }
            }
        }
    }

    public function usuarios() {
        return $this->hasMany('App\Cadastros', 'codigo', 'idcadastro');
    }

    public function lote() {
        return $this->hasOne('App\Lotes', 'codigo', 'idlote');
    }

    public function compraParcelada() {
        return $this->hasOne('App\ComprasParceladas', 'idlance', 'codigo');
    }

}