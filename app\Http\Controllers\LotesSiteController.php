<?php

namespace App\Http\Controllers;

use Auth;
use App\Lotes;
use Carbon\Carbon;
use App\Mail\NotificaAdms;
use App\CadastrosDocumentos;
use Illuminate\Http\Request;
use App\CadastrosHabilitados;
use App\Routines\AberturaLeilao;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Routines\EncerramentoLeilao;
use Illuminate\Support\Facades\Mail;

class LotesSiteController extends Controller
{
    private function getInfoLote($request, $id, $user)
    {
        $lote = Lotes::with([
            'imagensLote',
            'leilao',
            'anexosLote',
            'maiorLance',
            'maiorLanceParcelado',
            'lanceParceladoEscolhido'
        ])->findOrFail($id);

        if ($lote['leilao']->suspender == 1) {
            return redirect('/')->with('success', 'Este registro não foi encontrado');
        }

        // Obtém o fuso horário com base no estado do lote
        if ($lote->num_processo) {
            $timezone = getFuso($lote->num_processo);
        } else {
            $timezone = $this->getTimezoneByUF($lote->estado->uf);
        }
        Carbon::setLocale('pt_BR');
        date_default_timezone_set($timezone);

        $leiloes = DB::select('
        SELECT * FROM (
            SELECT * FROM (
                SELECT A.*, B.categoria, B.codigo AS codigolote, B.encerrado AS statuslote, 
                B.idleilao, B.leilao_data_final AS datafinal1, B.leilao_hora_final AS horafinal1, 
                B.leilao2_data_final AS datafinal2, B.leilao2_hora_final AS horafinal2, 
                B.destaque AS destaque_lote, B.lance_data_1, B.lance_data_2
                FROM leiloes A
                INNER JOIN lotes B ON A.codigo = B.idleilao AND B.codigo != :id
                WHERE B.idestado = :idestado
                AND B.idcidade = :idcidade
                AND B.bairro = :bairro
                AND (A.encerrado = 1 OR A.encerrado = 7)
                AND B.categoria = :categoria
                AND A.suspender != 1
                AND A.deleted_at IS NULL 
                AND B.deleted_at IS NULL
                ORDER BY A.leilao2_data_final ASC, A.leilao2_hora_final ASC, B.categoria ASC 
                LIMIT 8
            ) AS first
            UNION
            SELECT * FROM (
                SELECT A.*, B.categoria, B.codigo AS codigolote, B.encerrado AS statuslote, 
                B.idleilao, B.leilao_data_final AS datafinal1, B.leilao_hora_final AS horafinal1, 
                B.leilao2_data_final AS datafinal2, B.leilao2_hora_final AS horafinal2, 
                B.destaque AS destaque_lote, B.lance_data_1, B.lance_data_2
                FROM leiloes A
                INNER JOIN lotes B ON A.codigo = B.idleilao AND B.codigo != :id
                WHERE B.idestado = :idestado
                AND B.idcidade = :idcidade
                AND B.bairro != :bairro
                AND (A.encerrado = 1 OR A.encerrado = 7)
                AND B.categoria = :categoria
                AND A.suspender != 1
                AND A.deleted_at IS NULL 
                AND B.deleted_at IS NULL
                ORDER BY A.leilao2_data_final ASC, A.leilao2_hora_final ASC, B.categoria ASC 
                LIMIT 8
            ) AS sec
        ) AS todos', [
            'id' => $id,
            'idestado' => (int)$lote->idestado,
            'idcidade' => (int)$lote->idcidade,
            'bairro' => addslashes($lote->bairro),
            'categoria' => $lote->categoria
        ]);

        if (count($leiloes) == 0) {
            $leiloes = leiloesDestaque();
        }

        $estaHabilitado = CadastrosHabilitados::where('idcadastro', @$user->codigo)
            ->where('idlote', $id)
            ->count();

        $valor = number_format(lanceMinino($lote), 2, ",", ".") ?? 0;

        // Conversão das datas respeitando o fuso horário
        $dataInicial = Carbon::parse("{$lote->leilao_data_final} {$lote->leilao_hora_final}", $timezone)
            ->translatedFormat('d/m/Y H:i:s');

        $dataInicial2 = Carbon::parse("{$lote->leilao2_data_final} {$lote->leilao2_hora_final}", $timezone)
            ->translatedFormat('d/m/Y H:i:s');

        // Gerando HTML dos lances mínimos
        $htmlLanceMinimo = '';
        $lanceValor = lanceMinino($lote);
        $incremento = $lote->incremento;
        $valorPrimeiroLance = number_format($lanceValor, 2, ',', '.');

        $primeiroLance = '<li><a href="javascript:;" onclick="javascript: confirmacao_lance_form(\'' . $valorPrimeiroLance . '\')">
        Enviar o lance de ' . $valorPrimeiroLance . ' </a></li>';

        for ($i = 1; $i <= 3; $i++) {
            $valorIncremento = number_format($lanceValor + ($incremento * $i), 2, ',', '.');

            $htmlLanceMinimo .= '<li><a href="javascript:;" onclick="javascript: confirmacao_lance_form(\'' . $valorIncremento . '\')">
            + R$ ' . number_format(($incremento * $i), 2, ',', '.') . '</a></li>';
        }

        return [
            'loteAtual' => $id,
            'htmlLanceMinimo' => $primeiroLance . $htmlLanceMinimo,
            'today' => getdate(),
            'data_final' => $dataInicial,
            'data_final2' => $dataInicial2,
            'valor' => $valor,
            'dataCronometro' => retornaDataCronometro($lote, 1),
            'md5' => md5($lote),
            'lote' => $lote,
            'estaHabilitado' => $estaHabilitado,
            'user' => Auth::user(),
            'request' => $request,
            'leiloes' => $leiloes,
            'ultimosLances' => ultimosLancesOfertados($lote),
            'maiorLance' => maiorLance($lote)
        ];
    }

    /**
     * Retorna o fuso horário baseado na UF do estado.
     */
    private function getTimezoneByUF($uf)
    {
        $timezones = [
            'AC' => 'America/Rio_Branco',
            'AL' => 'America/Maceio',
            'AP' => 'America/Belem',
            'AM' => 'America/Manaus',
            'BA' => 'America/Bahia',
            'CE' => 'America/Fortaleza',
            'DF' => 'America/Sao_Paulo',
            'ES' => 'America/Sao_Paulo',
            'GO' => 'America/Sao_Paulo',
            'MA' => 'America/Fortaleza',
            'MT' => 'America/Cuiaba',
            'MS' => 'America/Campo_Grande',
            'MG' => 'America/Sao_Paulo',
            'PA' => 'America/Belem',
            'PB' => 'America/Fortaleza',
            'PR' => 'America/Sao_Paulo',
            'PE' => 'America/Recife',
            'PI' => 'America/Fortaleza',
            'RJ' => 'America/Sao_Paulo',
            'RN' => 'America/Fortaleza',
            'RS' => 'America/Sao_Paulo',
            'RO' => 'America/Porto_Velho',
            'RR' => 'America/Boa_Vista',
            'SC' => 'America/Sao_Paulo',
            'SP' => 'America/Sao_Paulo',
            'SE' => 'America/Maceio',
            'TO' => 'America/Araguaina',
        ];

        return $timezones[$uf] ?? 'America/Sao_Paulo';
    }

    /**
     * Show the profile for the given user.
     *
     * @param  int  $id
     * @return View
     */
    public function lote(Request $request, int $id)
    {
        $user = Auth::user();

        if ($request->ajax()) {

            return response()->json($this->getInfoLote($request, $id, $user));
        }

        EncerramentoLeilao::encerramento($id);
        AberturaLeilao::abertura($id);

        $infoLote = $this->getInfoLote($request, $id, $user);

        if (!is_array($infoLote)) {
            return $infoLote;
        }

        $lotesDoLeilao = Lotes::where('idleilao', $infoLote['lote']['leilao']->codigo)->get();

        $infoLote['lotesdoleilao'] = $lotesDoLeilao->toArray();

        return view('site.leiloes.lote', $infoLote);
    }

    public function lotes(Request $request, int $id)
    {
        $id = (int) $id;
        $lotes = Lotes::with('leilao', 'maiorLance.usuarios', 'maiorLanceParcelado', 'imagensLote')
            ->where('idleilao', $id)
            ->orderBy('numero_lote', 'asc')
            ->paginate(15);

        if ($request->ajax()) {
            return view('site.leiloes.lotes-content', ['lotes' => $lotes, 'user' => Auth::user(), 'request' => $request]);
        }
        return view('site.leiloes.lotes', ['lotes' => $lotes, 'user' => Auth::user(), 'request' => $request]);
    }

    public function habilitarLeilao(Request $request, $id)
    {
        $user = Auth::user();
        $lote = Lotes::findOrFail($id);
        $urlTitulo = urlTitulo($lote->titulo);
        $redirect = redirectLocal($lote);

        if (!$user) {
            return redirect($redirect)->with('success', 'Para habilitar-se em um leilão entre com seus dados cadastrados!');
        }

        if (
            CadastrosHabilitados::where('idcadastro', $user->codigo)
            ->where('idlote', $id)
            ->get()->count() > 0
        ) {
            return redirect($redirect)->with('success', 'VOCÊ JÁ ESTÁ HABILITADO (A) PARA OFERTAR LANCES NESTE LEILÃO!');
        }

        $documentos = CadastrosDocumentos::where('idcadastro', $user->codigo)
            ->where('status_pessoal', 1)
            ->where('status_residencia', 1)
            ->get();
        if ($documentos->count() == 0) {
            return redirect($redirect)->with('success', 'Para habilitar-se, acesse a sua área restrita e envie seus documentos acessando: MINHA CONTA > CADASTRO > DOCUMENTOS. Após o envio aguarde aprovação de nossa equipe.');
        }

        $leilao = Lotes::select('idleilao')->where('codigo', $lote->codigo)->first();

        //seleciona todos os lotes do leilao
        $lotes = Lotes::where('idleilao', $leilao->idleilao)->get();
        foreach ($lotes as $loteHabilita) {
            CadastrosHabilitados::updateOrCreate(
                ['idcadastro' => $user->codigo, 'idlote' => $loteHabilita->codigo],
                [
                    'idlote' => $loteHabilita->codigo,
                    'idcadastro' => $user->codigo,
                    'data_habilitacao' => date('Y-m-d H:i:s')
                ]
            );
        }

        $nome = $user->pessoa == 2 ? $user->razao_social : $user->nome;
        $texto  = 'O cliente ' . $nome . ', E-mail: ' . $user->email . ', acaba de se habilitar no leilão: ' . $lotes[0]->leilao->cod_referencia . '!<br>';
        Mail::to('<EMAIL>')->queue(new NotificaAdms('Novo habilitado no leilão: ' . $lotes[0]->leilao->cod_referencia, $texto));

        return redirect($redirect)->with('success', 'VOCÊ JÁ ESTÁ HABILITADO (A) PARA OFERTAR LANCES NESTE LEILÃO!');
    }

    public function rotinasLeilao(Request $request)
    {
        $ip = $_SERVER['REMOTE_ADDR'];
        if ($ip != '127.0.0.1') {
            return 'false';
        }
        EncerramentoLeilao::encerramento(0);
        AberturaLeilao::abertura(0);
        return 'true';
    }
}
