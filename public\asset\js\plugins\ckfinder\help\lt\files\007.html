<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>CK<PERSON>inder vartotojo instrukcija</title>
	<link href="../../files/other/help.css" type="text/css" rel="stylesheet" />
	<script type="text/javascript" src="../../files/other/help.js"></script>
	<meta name="robots" content="noindex, nofollow" />
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" /></head>
<body>
	<h1>
		Atnaujinimo mygtukas
	</h1>
	<p>
		Dirbant bendro naudojimo aplinkoje su CKFinder, kur daugelį failų adminstruoja
        kiti vartotojai, failai gali būti pakeisti tuo pačiu metu.
		Šių failų sąrašui atnaujinti, naudokite mygtuką "Atnaujinti"
        </p>
	<p style="text-align: center">
		<img height="25" src="images/011.gif" width="258" />&nbsp;</p>
	<p>
		Pavyzdžiui, Jums reikia sukurti naują prekę puslapiui.
		Jūs atidarote CKFinder, kad surastumėte tinkamą nuotrauką, bet kai atidarote
		"Prekės" segtuvą, to paveiksliuko nerandate. Tada skambinate "Beatai" ir sakote:
		"Labas Beata, prekės paveiksliuko nėra CKFinder programoje!".
		Beata atsako, "Oi... luktelkit". Ji atveria CKFinder savo kompiuteryje, įkelia
		paveiksliuką iš savo desktop'o ir sako jums: "Jau yra, tik atnaujinkite puslapį".
		Jūs nuspaudžiate "Atnaujinti" ir viskas užveikia! Failas tampa matomas.
        Štai kodėl CKFinder dar kitaip yra vadinamas kaip <strong>bendro naudojimo programa.</strong></p>
</body>
</html>
