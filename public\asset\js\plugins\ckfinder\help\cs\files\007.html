<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>Uživatelská příručka CKFinder</title>
	<link href="../../files/other/help.css" type="text/css" rel="stylesheet" />
	<script type="text/javascript" src="../../files/other/help.js"></script>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta name="robots" content="noindex, nofollow" />
</head>
<body>
	<h1>
		Tlačít<PERSON> Znovu načíst</h1>
	<p>K<PERSON><PERSON> pracujete ve sdíleném prostředí, k<PERSON><PERSON> spravu<PERSON>, kde desítky či dokonce stovky
		uživatelů pracuje na stejných souborech ve stejnou dobu, mů<PERSON><PERSON> se stát, že
		ostatní přidají změny souborů či složek, s kterými pracujete,
		nebo si je právě prohlížíte.</p>
	<p>Tady se může hodit možnost <strong>Znovu načíst (Refresh)</strong>. Tato funkce
		Vám umožňuje znovu načíst obsahy složky a vidět její nejnovější stav. Kdykoliv
		potřebujete vidět aktualizovaný seznam souborů, klikněte na tlačítko <strong>Znovu načíst (Refresh)</strong>
		v <strong><a href="005.html">Panelu nástrojů</a></strong> CKFinder.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_toolbar_refresh.png" width="251" height="64" alt="Tlačítko Znovu načíst v Panelu nástrojů CKFinder" />&nbsp;</p>
	<p>
		Tato funkce je zvláště užitečná, pokud CKFinder používáte jako <em>software pro
		spolupráci</em>, sdílení souborů a složek s přáteli, rodinou, kolegy, nebo jakoukoli jinou
		skupinou uživatelů. Abyste zajistili, že vždycky uvidíte tu nejaktuálnější verzi
		obsahu v CKFinder, budete chtít občas znovu načíst seznam souborů pro
		jeho aktualizaci.</p>
</body>
</html>
