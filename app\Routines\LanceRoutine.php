<?php

namespace App\Routines;

use DateTime;
use App\Lotes;
use App\Lances;
use App\Leiloes;
use App\UserSite;
use DateTimeZone;
use App\LoteAviso;
use App\Jobs\LanceAutomatico;
use Illuminate\Support\Facades\Log;

class LanceRoutine
{
    public function addLance($leilaoInfo, $codigo, $idUsuario)
    {
        $lote = $leilaoInfo->lote;
        if ($lote->num_processo) {
            $timezone = getFuso($lote->num_processo);
        } else {
            $timezone = self::getTimezoneByUF($lote->estado->uf);
        }

        // Usar DateTime com timezone explícito
        $now = new DateTime('now', new DateTimeZone($timezone));

        $indCronometro = 0;
        if ($this->addMinutes($lote)) {
            $indCronometro = 1;
        }

        $usuario = new UserSite();
        $user = $usuario->find($idUsuario);
        $nome = $user->nome != '' ? $user->nome : $user->razao_socal;

        $lance = new Lances;
        $lance->idcadastro = $idUsuario;
        $lance->desativado = 0;
        $lance->idlote = (int) $codigo;
        $lance->valor = $leilaoInfo->valorLance;
        $lance->data_lance = $now->format('Y-m-d H:i:s');
        $lance->data_cronometro = $now->format('Y-m-d H:i:s');
        $lance->ind_cronometro = $indCronometro;

        $automatico = false;
        if (isset($leilaoInfo->lance_automatico)) {
            $lance->automatico = 1;
            $automatico = true;
        }

        $lance->save();

        if (!isset($leilaoInfo->automatico)) {
            LanceAutomatico::dispatch($lance)->delay(now()->addSeconds(10));
        }

        if ($automatico == false) {
            Log::channel('lances')->info('Novo lance efetuado. Lote [' . $codigo . '] Usuário: ' . $nome);
        }

        $lote = Lotes::find($lance->idlote);
        LoteAviso::criaAvisoLanceSuperado($lote, $lote->habilitadosComLances()->get(), $idUsuario);
        return true;
    }

    public function addMinutes(Lotes $lote)
    {
        $timezone = getFuso($lote->num_processo);
        $timezoneObj = new DateTimeZone($timezone);

        $now = new DateTime('now', $timezoneObj);
        $mktimeHoje = $now->getTimestamp();

        // Converter todas as datas para DateTime com timezone
        $primeiroInicio = DateTime::createFromFormat('Y-m-d H:i:s', $lote->leilao_data_inicial . ' ' . $lote->leilao_hora_inicial, $timezoneObj);
        $timeLeilao1 = DateTime::createFromFormat('Y-m-d H:i:s', $lote->leilao_data_final . ' ' . $lote->leilao_hora_final, $timezoneObj);
        $timeLeilao2 = DateTime::createFromFormat('Y-m-d H:i:s', $lote->leilao2_data_final . ' ' . $lote->leilao2_hora_final, $timezoneObj);

        $atualiza5Minutos = 'leilao_data_final';
        $atualizaHora5Min = 'leilao_hora_final';
        $minutosRestantes = $timeLeilao1->getTimestamp() - $mktimeHoje;
        $incrementaMinuto = $timeLeilao1->getTimestamp() + 180;

        $segundaPraca = false;
        if ($mktimeHoje >= $timeLeilao1->getTimestamp()) {
            $atualiza5Minutos = 'leilao2_data_final';
            $atualizaHora5Min = 'leilao2_hora_final';
            $minutosRestantes = $timeLeilao2->getTimestamp() - $mktimeHoje;
            $incrementaMinuto = $timeLeilao2->getTimestamp() + 180;
            $segundaPraca = true;
        }

        if ($minutosRestantes <= 180 && $minutosRestantes > 0) {
            $leilao = Leiloes::find($lote->leilao['codigo']);

            if ($leilao->tipo != 4 || (int) $leilao->usar_cronometro > 0) {
                $script = __DIR__ . '/../../scripts/encerra-leilao.php ' . (int) $lote->codigo;
                file_put_contents('/tmp/comando', "/usr/bin/php7.4  $script");
                $execResult = exec("nohup nice -20 /usr/bin/php7.4  $script > /dev/null & echo $!");
            }

            $addTime = 180 - $minutosRestantes;
            $incrementaMinuto = $timeLeilao1->getTimestamp() + $addTime;

            if ($segundaPraca) {
                $incrementaMinuto = $timeLeilao2->getTimestamp() + $addTime;
            }

            $novaData = (new DateTime())->setTimestamp($incrementaMinuto)->setTimezone($timezoneObj);

            $leilao->$atualiza5Minutos = $novaData->format('Y-m-d');
            $leilao->$atualizaHora5Min = $novaData->format('H:i:s');

            $lote->$atualiza5Minutos = $novaData->format('Y-m-d');
            $lote->$atualizaHora5Min = $novaData->format('H:i:s');

            if ($timeLeilao1 == $timeLeilao2) {
                $incrementaMinuto = $timeLeilao1->getTimestamp() + $addTime;
                $novaDataUnica = (new DateTime())->setTimestamp($incrementaMinuto)->setTimezone($timezoneObj);

                $leilao->leilao_data_final = $novaDataUnica->format('Y-m-d');
                $leilao->leilao_hora_final = $novaDataUnica->format('H:i:s');
                $leilao->leilao2_data_final = $novaDataUnica->format('Y-m-d');
                $leilao->leilao2_hora_final = $novaDataUnica->format('H:i:s');

                $lote->leilao_data_final = $novaDataUnica->format('Y-m-d');
                $lote->leilao_hora_final = $novaDataUnica->format('H:i:s');
                $lote->leilao2_data_final = $novaDataUnica->format('Y-m-d');
                $lote->leilao2_hora_final = $novaDataUnica->format('H:i:s');
            }

            $lote->fechamento = $novaData->format('d/m/Y H:i:s');
            $lote->save();
            $leilao->save();
            return true;
        }
        return false;
    }

    public static function getTimezoneByUF($uf)
    {
        // Mapeia os estados brasileiros para seus respectivos fusos horários
        $timezones = [
            'AC' => 'America/Rio_Branco',
            'AL' => 'America/Maceio',
            'AP' => 'America/Belem',
            'AM' => 'America/Manaus',
            'BA' => 'America/Bahia',
            'CE' => 'America/Fortaleza',
            'DF' => 'America/Sao_Paulo',
            'ES' => 'America/Sao_Paulo',
            'GO' => 'America/Sao_Paulo',
            'MA' => 'America/Fortaleza',
            'MT' => 'America/Cuiaba',
            'MS' => 'America/Campo_Grande',
            'MG' => 'America/Sao_Paulo',
            'PA' => 'America/Belem',
            'PB' => 'America/Fortaleza',
            'PR' => 'America/Sao_Paulo',
            'PE' => 'America/Recife',
            'PI' => 'America/Fortaleza',
            'RJ' => 'America/Sao_Paulo',
            'RN' => 'America/Fortaleza',
            'RS' => 'America/Sao_Paulo',
            'RO' => 'America/Porto_Velho',
            'RR' => 'America/Boa_Vista',
            'SC' => 'America/Sao_Paulo',
            'SP' => 'America/Sao_Paulo',
            'SE' => 'America/Maceio',
            'TO' => 'America/Araguaina',
        ];

        return $timezones[$uf] ?? 'America/Sao_Paulo'; // Default para São Paulo caso o estado não seja encontrado
    }
}
