<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\LeiloesCurtidos;
use App\Lotes;

class CurtirLoteController extends Controller
{
    public function curtirDescurtir($idlote, $idLeilao) {
        $lote = Lotes::where('codigo', $idlote)->where('idleilao', $idLeilao)->first();

        if($lote) {
            $user = \Auth::user();

            $loteCurtido = LeiloesCurtidos::where('idlote', $idlote)
                                           ->where('idleilao', $idLeilao)
                                           ->where('idusuario', $user->codigo)
                                           ->first();
            // descurtir
            if($loteCurtido) {
                $loteCurtido->delete();
                return response()->json(['msg' => 'success', 'icon' => 'fa-heart-o'], 200);
            }

            // curtir
            $loteCurtido = LeiloesCurtidos::create([
                'idlote' => $idlote,
                'idleilao' => $idLeilao,
                'idusuario' => $user->codigo,
            ]);

            if ($loteCurtido) {
                return response()->json(['msg' => 'success', 'icon' => 'fa-heart'], 200);
            }
            return response()->json(['msg' => 'Erro desconhecido'], 500);
        }

        return response()->json(['msg' => 'Erro desconhecido'], 500);
    }
}
