<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use App\Jobs\LanceSuperado;
use Illuminate\Support\Facades\Log;

class LoteAviso extends Model
{
    protected $fillable = [
        'tipo_aviso',
        'idlote',
        'idcadastro'
    ];

    public static function criaAvisoLanceSuperado(\App\Lotes $lote, $habilitados, $idUsuarioLanceAtual)
    {
        foreach ($habilitados as $habilitado) {
            if ($habilitado->codigo == $idUsuarioLanceAtual) {
                continue;
            }

            if (self::avisoAtivo($lote->codigo, $habilitado->codigo) === null) {
                $aviso = self::create([
                    'tipo_aviso' => 'lance_superado',
                    'idcadastro' => $habilitado->codigo,
                    'idlote' => $lote->codigo,
                ]);

                if ($aviso) {
                    Log::info("LoteAviso criado com sucesso", ['id' => $aviso->id]);
                    LanceSuperado::dispatch($lote->codigo, $habilitado->codigo)->delay(now()->addSeconds(60));
                } else {
                    Log::error("Falha ao criar LoteAviso", [
                        'idlote' => $lote->codigo,
                        'idcadastro' => $habilitado->codigo,
                    ]);
                }
            }
        }
    }

    public static function avisoAtivo($loteCodigo, $usuarioDestinatario)
    {
        return self::where('tipo_aviso', 'lance_superado')
            ->where('idcadastro', $usuarioDestinatario)
            ->where('idlote', $loteCodigo)
            ->first();
    }
}
