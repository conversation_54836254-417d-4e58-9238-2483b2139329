<?php

namespace App\Services;
use App\Cadastros;

class RelatorioResultadoLeilaoService {
    public function exportaTransacoesLeilao(\App\Leiloes $leilao) {
        $csv = "Resultado do Leilão;;;\r\n";
        $csv .= "Número do lote;Descrição do lote;Número do processo;Valor inicial;Valor de avaliação;Valor do lance;Data/Hora do Lance;Quantidade de lances;Endereço;Bairro;Cidade;Estado;Login;Nome do arrematante;CPF/CNPJ;RG/Inscrição Estadual;Data de nascimento;Estado Civil;Tipo de União;Nome (Conjuge);CPF (Conjuge);RG (Conjuge);Endereço;Nº;Complemento;Bairro;Cidade;Estado;CEP;Telefone 1;Telefone 2;E-mail\r\n";

        foreach($leilao->lots as $lote) {
            $lances = $lote->getLancesComCadastrosAttribute();

            if ($lances->count() > 0) {
                // Explicitly use the mysql_2 connection for user data
                $usuario = Cadastros::where('codigo', $lances[0]->idcadastro)
                    ->first();

                $maiorLance = @$lances[0]->valor ?? 0;
                $nome = $usuario->nome ?? $usuario->razao_social ?? null;
                $cpfCnpj = $usuario->cpf ?? $usuario->cnpj ?? null;
                $apelido = $usuario->apelido ?? null;
                $rg = $usuario->rg ?? $usuario->insc_estadual ?? null;
                
                $dataNascimento = null;
                if ($usuario->data_nascimento ?? null) {
                    $dataNascimento = explode('-', $usuario->data_nascimento);
                    $dataNascimento = $dataNascimento[2] . '/' . $dataNascimento[1] . '/' . $dataNascimento[0];
                }
                
                $conjuge = $usuario->conjuge ?? null;
                $cpfConjuge = $usuario->c_cpf ?? null;
                $rgConjuge = $usuario->c_rg ?? null;
                $endereco = $usuario->endereco ?? null;
                $numero = $usuario->numero ?? null;
                $complemento = $usuario->complemento ?? null;
                $bairro = $usuario->bairro ?? null;
                $cidade = $usuario->cidade ?? null;
                $estado = $usuario->estado ?? null;
                $cep = $usuario->cep ?? null;
                $telefone = $usuario->telefone ?? null;
                $celular = $usuario->celular ?? null;
                $email = $usuario->email ?? null;

                $csv .= $lote->numero_lote .  ';' . $lote->titulo . ';' . $lote->num_processo . ';R$' . number_format($lote->lance_data_1, 2, ",", ".") . ';R$' . number_format($lote->avaliacao, 2, ",", ".");
                $csv .= ';R$' . number_format($maiorLance, 2, ",", ".") .  ';'
                . @$lances[0]->data_lance . ';'
                . $lances->count() . ';'.$lote->endereco.';'.$lote->bairro.';'.\App\Cidades::find($lote->idcidade)->nome.';'.\App\Estados::find($lote->idestado)->nome.';'. $apelido . ';' . $nome. ';' . $cpfCnpj;
                $csv .= ';' . $rg .  ';' . $dataNascimento . ';' . $this->estadoCivil($usuario) . ';' . $this->tipoUniao($usuario);
                $csv .= ';' . $conjuge .  ';' . $cpfConjuge . ';' . $rgConjuge . ';' . $endereco . ';' . $numero;
                $csv .= ';' . $complemento .  ';' . $bairro . ';' . $cidade . ';' . $estado . ';' . $cep;
                $csv .= ';' . $telefone .  ';' . $celular . ';' . $email . "\r\n";
            } else {
                $csv .= $lote->numero_lote .  ';' . $lote->titulo . ';' . $lote->num_processo . ';R$' . number_format($lote->lance_data_1, 2, ",", ".") . ';R$' . number_format($lote->avaliacao, 2, ",", ".");
                $csv .= ';R$' . number_format(0, 2, ",", ".") .  ';0;;;;';
                $csv .= ';;;;';
                $csv .= ';;;;;';
                $csv .= ';;;;;';
                $csv .= ";;;\r\n";
            }
        }

        $dir = __DIR__ . '/../../tmp';
        if (!is_dir($dir)) {
            mkdir($dir);
        }

        $file = 'resultado-leilao-' . \Str::slug($leilao->titulo) . '.csv';
        $fileName = $dir . '/' . $file;
        if (file_exists($fileName)) {
            unlink($fileName);
        }
        $arq = fopen($fileName, 'a+');
        fwrite($arq, $csv);
        fclose($arq);
        return $file;
    }

    private function tipoUniao($cadastro) {
        if ($cadastro) {
            $tipoUniao = '';
            switch($cadastro->tipo_uniao) {
                case "1":
                    $tipoUniao = 'Comunhão Parcial de Bens';
                break;
                case "2":
                    $tipoUniao = 'Comunhão Universal de Bens';
                break;
                case "3":
                    $tipoUniao = 'Separação de Bens';
                break;
                default:
                    $tipoUniao = '';
                break;
            }
            return $tipoUniao;
        }

        return null;
    }

    private function estadoCivil($cadastro) {
        if ($cadastro) {
            $estadoCivil = '';
            switch($cadastro->estado_civil) {
                case "1":
                    $estadoCivil = 'Solteiro(a)';
                break;
                case "2":
                    $estadoCivil = 'Casado(a)';
                break;
                case "3":
                    $estadoCivil = 'Separado(a)';
                break;
                case "4":
                    $estadoCivil = 'Divorciado';
                break;
                case "5":
                    $estadoCivil = 'Viúvo(a)';
                break;
            }
            return $estadoCivil;
        }

        return null;
    }

    private function formataData($data, $hora = true) {
        if (!$hora) {
            return \Carbon\Carbon::createFromFormat('Y-m-d', $data)->format('d/m/Y');
        }
        return \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $data)->format('d/m/Y H:i:s');
    }

}

