<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Auth;

class CadastroLeiloesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'tipo' => 'required',
            'modalidade' => 'required',
            'titulo' => 'required',
            #'cod_referencia' => ['required', Rule::unique('leiloes')->ignore($this->codigo, 'codigo')],
            'subtitulo' => 'required',
            'leilao_data_tipo' => 'requiredif:tipo_leilao,!=,9',
            'leilao_data_inicial' =>  function ($attribute, $value, $fail) {
                global $request;
                if ($request['encerrado'] == '9') {
                    return;
                }
                $leilaoDataTipo = (int) $request['leilao_data_tipo'];
                $request['leilao_data_inicial'] = trim ($request['leilao_data_inicial']);
                if ($request['leilao_data_inicial'] == '' && $leilaoDataTipo != 3) {
                    $fail('A data inicial da primeira praça deve ser indicada');
                    return;
                }
                if (strlen($request['leilao_data_inicial']) != 10) {
                    $fail('A data inicial da primeira praça não é uma data válida.');
                    return;
                }
                $leilao2DataInicial = explode('/', $request['leilao_data_inicial']);
                if (count($leilao2DataInicial) !=3 ) {
                    $fail('A data inicial da primeira praça não é uma data válida.');
                    return;
                }
                if (!checkdate($leilao2DataInicial[1], $leilao2DataInicial[0], $leilao2DataInicial[2])) {
                    $fail('A data inicial da primeira praça não é uma data válida.');
                    return;
                }
                
            },
            'leilao_hora_inicial' => function ($attribute, $value, $fail) {
                global $request;
                if ($request['encerrado'] == '9') {
                    return;
                }
                $leilaoDataTipo = (int) $request['leilao_data_tipo'];
                $request['leilao_hora_inicial'] = trim ($request['leilao_hora_inicial']);
                if ($request['leilao_hora_inicial'] == '' && $leilaoDataTipo != 3) {
                    $fail('A hora inicial da primeira praça deve ser indicada');
                    return;
                }
                $request['leilao_data_inicial'] = trim($request['leilao_data_inicial']);
                if ($request['leilao_data_inicial'] == '') {
                    $fail('A data inicial da primeira praça deve ser indicada');
                    return;
                }
                $leilao2DataInicial = explode('/', $request['leilao_data_inicial']);
                if (count($leilao2DataInicial) != 3) {
                    $fail('A data inicial da primeira praça deve ser indicada');
                    return;
                }

                $leilao2DataInicial = $leilao2DataInicial[2] . '-'. $leilao2DataInicial[1] . '-' . $leilao2DataInicial[0];
                $leilao2DataInicial = $leilao2DataInicial. ' ' . $request['leilao_hora_inicial'];
                
                if (strlen($leilao2DataInicial) < 16) {
                    $fail('A hora inicial da primeira praça não é uma hora válida.');
                    return;
                }
                
                if (strlen($leilao2DataInicial) > 16 && strlen($leilao2DataInicial) > 19) {
                    $fail('A hora inicial da primeira praça não é uma hora válida.');
                    return;
                }
                
                if (strlen($leilao2DataInicial) == 19) {
                    
                    $hora = explode(':', $request['leilao_hora_inicial']);
                    if (count($hora) != 3) {
                        $fail('A hora inicial da primeira praça não é uma hora válida.');
                        return;
                    }
                    if ((int)$hora[2] > 59) {
                        $fail('A hora inicial da primeira praça não é uma hora válida.');
                        return;
                    }
                    if ((int)$hora[1] > 59) {
                        $fail('A hora inicial da primeira praça não é uma hora válida.');
                        return;
                    }

                    if ((int)$hora[0] > 24) {
                        $fail('A hora inicial da primeira praça não é uma hora válida.');
                        return;
                    }
                }
                
                if (strlen($leilao2DataInicial) == 16) {
                    $hora = explode(':', $request['leilao_hora_inicial']);
                    if (count($hora) != 2) {
                        $fail('A hora inicial da primeira praça não é uma hora válida.');
                        return;
                    }
                    if ((int)$hora[1] > 59) {
                        $fail('A hora inicial da primeira praça não é uma hora válida.');
                        return;
                    }

                    if ((int)$hora[0] > 24) {
                        $fail('A hora inicial da primeira praça não é uma hora válida.');
                        return;
                    }
                }
            },
            'leilao_data_final' => function ($attribute, $value, $fail) {
                global $request;
                if ($request['encerrado'] == '9') {
                    return;
                }
                $leilaoDataTipo = (int) $request['leilao_data_tipo'];
                $request['leilao_data_final'] = trim ($request['leilao_data_final']);
                if ($request['leilao_data_final'] == '' && $leilaoDataTipo != 3) {
                    $fail('A data final da primeira praça deve ser indicada');
                    return;
                }
                if (strlen($request['leilao_data_final']) != 10) {
                    $fail('A data final da primeira praça não é uma data válida.');
                    return;
                }
                $leilao2DataInicial = explode('/', $request['leilao_data_final']);
                if (count($leilao2DataInicial) !=3 ) {
                    $fail('A data final da primeira praça não é uma data válida.');
                    return;
                }

                if (!checkdate($leilao2DataInicial[1], $leilao2DataInicial[0], $leilao2DataInicial[2])) {
                    $fail('A data final da primeira praça não é uma data válida.');
                    return;
                }
                
            },
            'leilao_hora_final' => function ($attribute, $value, $fail) {
                global $request;
                if ($request['encerrado'] == '9') {
                    return;
                }
                $leilaoDataTipo = (int) $request['leilao_data_tipo'];
                $request['leilao_hora_final'] = trim ($request['leilao_hora_final']);
                if ($request['leilao_hora_final'] == '' && $leilaoDataTipo != 3) {
                    $fail('A hora final da primeira praça deve ser indicada');
                    return;
                }
                $request['leilao_data_inicial'] = trim($request['leilao_data_inicial']);
                if ($request['leilao_data_inicial'] == '') {
                    $fail('A data inicial da primeira praça deve ser indicada');
                    return;
                }
                $leilao2DataInicial = explode('/', $request['leilao_data_inicial']);
                if (count($leilao2DataInicial) != 3) {
                    $fail('A data inicial da primeira praça deve ser indicada');
                    return;
                }

                $leilao2DataInicial = $leilao2DataInicial[2] . '-'. $leilao2DataInicial[1] . '-' . $leilao2DataInicial[0];
                $leilao2DataInicial = $leilao2DataInicial. ' ' . $request['leilao_hora_final'];

                if (strlen($leilao2DataInicial) < 16) {
                    $fail('A hora final da primeira praça não é uma hora válida.');
                    return;
                }
                
                if (strlen($leilao2DataInicial) > 16 && strlen($leilao2DataInicial) > 19) {
                    $fail('A hora final da primeira praça não é uma hora válida.');
                    return;
                }

                if (strlen($leilao2DataInicial) == 19) {
                    $hora = explode(':', $request['leilao_hora_final']);
                    if (count($hora) != 3) {
                        $fail('A hora final da primeira praça não é uma hora válida.');
                        return;
                    }
                    if ((int)$hora[2] > 59) {
                        $fail('A hora final da primeira praça não é uma hora válida.');
                        return;
                    }
                    if ((int)$hora[1] > 59) {
                        $fail('A hora final da primeira praça não é uma hora válida.');
                        return;
                    }

                    if ((int)$hora[0] > 24) {
                        $fail('A hora final da primeira praça não é uma hora válida.');
                        return;
                    }
                }

                if (strlen($leilao2DataInicial) == 16) {
                    $hora = explode(':', $request['leilao_hora_final']);
                    if (count($hora) != 2) {
                        $fail('A hora final da primeira praça não é uma hora válida.');
                        return;
                    }
                    if ((int)$hora[1] > 59) {
                        $fail('A hora final da primeira praça não é uma hora válida.');
                        return;
                    }

                    if ((int)$hora[0] > 24) {
                        $fail('A hora final da primeira praça não é uma hora válida.');
                        return;
                    }
                }
            },

            'leilao2_data_tipo' => function ($attribute, $value, $fail) {
                global $request;
                if ($request['encerrado'] == '9') {
                    return;
                }
                $leilaoDataTipo = (int) $request['leilao_data_tipo'];
                if ($leilaoDataTipo == 4) {
                    return;
                }
                $leilaoDataTipo2 = (int) $request['' . $attribute . ''];
                if ($leilaoDataTipo != 3 && $leilaoDataTipo != 0 && $leilaoDataTipo2 == 0) {
                    $fail('O tipo da segunda praça deve ser indicado');
                }
            },
            'leilao2_data_inicial' => function ($attribute, $value, $fail) {
                global $request;
                if ($request['encerrado'] == '9') {
                    return;
                }
                $leilaoDataTipo = (int) $request['leilao_data_tipo'];
                if ($leilaoDataTipo == 4) {
                    return;
                }
                $request['leilao2_data_inicial'] = trim ($request['leilao2_data_inicial']);
                if ($request['leilao2_data_inicial'] == '' && $leilaoDataTipo != 3) {
                    $fail('A data inicial da segunda praça deve ser indicada');
                    return;
                }
                if (strlen($request['leilao2_data_inicial']) != 10) {
                    $fail('A data inicial da segunda praça não é uma data válida.');
                    return;
                }
                $leilao2DataInicial = explode('/', $request['leilao2_data_inicial']);
                if (count($leilao2DataInicial) !=3 ) {
                    $fail('A data inicial da segunda praça não é uma data válida.');
                    return;
                }

                if (!checkdate($leilao2DataInicial[1], $leilao2DataInicial[0], $leilao2DataInicial[2])) {
                    $fail('A data inicial da segunda praça não é uma data válida.');
                    return;
                }
                
            },
            'leilao2_data_final' => function ($attribute, $value, $fail) {
                global $request;
                if ($request['encerrado'] == '9') {
                    return;
                }
                $leilaoDataTipo = (int) $request['leilao_data_tipo'];
                if ($leilaoDataTipo == 4) {
                    return;
                }
                $request['leilao2_data_final'] = trim ($request['leilao2_data_final']);
                $leilaoDataTipo2 = (int) $request['leilao2_data_tipo'];
                if ($request['leilao2_data_final'] == '' && $leilaoDataTipo != 3) {
                    $fail('A data final da segunda praça deve ser indicada');
                    return;
                }
                if (strlen($request['leilao2_data_final']) != 10) {
                    $fail('A data final da segunda praça não é uma data válida.');
                    return;
                }
                $leilao2DataFinal = explode('/', $request['leilao2_data_final']);
                if (!checkdate($leilao2DataFinal[1], $leilao2DataFinal[0], $leilao2DataFinal[2])) {
                    $fail('A data final da segunda praça não é uma data válida.');
                    return;
                }
                if ($leilaoDataTipo != 3 && $leilaoDataTipo != 0 && $leilaoDataTipo2 == 0) {
                    $fail('O tipo da segunda praça deve ser indicado');
                    return;
                }
            },
            'leilao2_hora_inicial' => function ($attribute, $value, $fail) {
                global $request;
                if ($request['encerrado'] == '9') {
                    return;
                }
                $leilaoDataTipo = (int) $request['leilao_data_tipo'];
                if ($leilaoDataTipo == 4) {
                    return;
                }
                $request['leilao2_hora_inicial'] = trim ($request['leilao2_hora_inicial']);
                if ($request['leilao2_hora_inicial'] == '' && $leilaoDataTipo != 3) {
                    $fail('A hora inicial da segunda praça deve ser indicada');
                    return;
                }
                $request['leilao2_data_inicial'] = trim($request['leilao2_data_inicial']);
                if ($request['leilao2_data_inicial'] == '') {
                    $fail('A data inicial da segunda praça deve ser indicada');
                    return;
                }
                $leilao2DataInicial = explode('/', $request['leilao2_data_inicial']);
                if (count($leilao2DataInicial) != 3) {
                    $fail('A data inicial da segunda praça deve ser indicada');
                    return;
                }

                $leilao2DataInicial = $leilao2DataInicial[2] . '-'. $leilao2DataInicial[1] . '-' . $leilao2DataInicial[0];
                $leilao2DataInicial = $leilao2DataInicial. ' ' . $request['leilao2_hora_inicial'];

                if (strlen($leilao2DataInicial) < 16) {
                    $fail('A hora inicial da segunda praça não é uma hora válida.');
                    return;
                }
                
                if (strlen($leilao2DataInicial) > 16 && strlen($leilao2DataInicial) > 19) {
                    $fail('A hora inicial da segunda praça não é uma hora válida.');
                    return;
                }

                if (strlen($leilao2DataInicial) == 19) {
                    $hora = explode(':', $request['leilao2_hora_inicial']);
                    if (count($hora) != 3) {
                        $fail('A hora inicial da segunda praça não é uma hora válida.');
                        return;
                    }
                    if ((int)$hora[2] > 59) {
                        $fail('A hora inicial da segunda praça não é uma hora válida.');
                        return;
                    }
                    if ((int)$hora[1] > 59) {
                        $fail('A hora inicial da segunda praça não é uma hora válida.');
                        return;
                    }

                    if ((int)$hora[0] > 24) {
                        $fail('A hora inicial da segunda praça não é uma hora válida.');
                        return;
                    }
                }

                if (strlen($leilao2DataInicial) == 16) {
                    $hora = explode(':', $request['leilao2_hora_inicial']);
                    if (count($hora) != 2) {
                        $fail('A hora inicial da segunda praça não é uma hora válida.');
                        return;
                    }
                    if ((int)$hora[1] > 59) {
                        $fail('A hora inicial da segunda praça não é uma hora válida.');
                        return;
                    }

                    if ((int)$hora[0] > 24) {
                        $fail('A hora inicial da segunda praça não é uma hora válida.');
                        return;
                    }
                }
            },
            'leilao2_hora_final' =>  function ($attribute, $value, $fail) {
                global $request;
                if ($request['encerrado'] == '9') {
                    return;
                }
                $leilaoDataTipo = (int) $request['leilao_data_tipo'];
                if ($leilaoDataTipo == 4) {
                    return;
                }
                $request['leilao2_hora_final'] = trim ($request['leilao2_hora_final']);
                if ($request['leilao2_hora_final'] == '' && $leilaoDataTipo != 3) {
                    $fail('A hora final da segunda praça deve ser indicada');
                    return;
                }
                $request['leilao2_data_final'] = trim($request['leilao2_data_final']);
                if ($request['leilao2_data_final'] == '') {
                    $fail('A data final da segunda praça não é uma data válida.');
                    return;
                }
                $leilao2DataInicial = explode('/', $request['leilao2_data_final']);
                if (count($leilao2DataInicial) != 3) {
                    $fail('A data final da segunda praça não é uma data válida.');
                    return;
                }
                $leilao2DataInicial = $leilao2DataInicial[2] . '-'. $leilao2DataInicial[1] . '-' . $leilao2DataInicial[0];
                $leilao2DataInicial = $leilao2DataInicial. ' ' . $request['leilao2_hora_final'];

                if (strlen($leilao2DataInicial) < 16) {
                    $fail('A hora final da segunda praça não é uma hora válida.');
                    return;
                }
                
                if (strlen($leilao2DataInicial) > 16 && strlen($leilao2DataInicial) > 19) {
                    $fail('A hora final da segunda praça não é uma hora válida.');
                    return;
                }

                if (strlen($leilao2DataInicial) == 19) {
                    $hora = explode(':', $request['leilao2_hora_final']);
                    if (count($hora) != 3) {
                        $fail('A hora final da segunda praça não é uma hora válida.');
                        return;
                    }
                    if ((int)$hora[2] > 59) {
                        $fail('A hora final da segunda praça não é uma hora válida.');
                        return;
                    }
                    if ((int)$hora[1] > 59) {
                        $fail('A hora final da segunda praça não é uma hora válida.');
                        return;
                    }

                    if ((int)$hora[0] > 24) {
                        $fail('A hora final da segunda praça não é uma hora válida.');
                        return;
                    }
                }

                if (strlen($leilao2DataInicial) == 16) {
                    $hora = explode(':', $request['leilao2_hora_final']);
                    if (count($hora) != 2) {
                        $fail('A hora final da segunda praça não é uma hora válida.');
                        return;
                    }
                    if ((int)$hora[1] > 59) {
                        $fail('A hora final da segunda praça não é uma hora válida.');
                        return;
                    }

                    if ((int)$hora[0] > 24) {
                        $fail('A hora final da segunda praça não é uma hora válida.');
                        return;
                    }
                }
            }
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'tipo.required' => 'Selecione o tipo do leilão.',
            'modalidade.required' => 'Selecione a modalidade do leilão.',
            'cod_referencia.unique' => 'Código de referência inválido pois já está em uso',
            'cod_referencia.required' => 'Código de referência deve ser informando',
            'titulo.required' => 'Defina um título para o leilão.',
            'subtitulo.required' => 'Defina um subtítulo para o leilão.',
            'leilao_data_tipo.required' => 'Defina o tipo de praça do leilão.',
            'leilao_data_inicial.required' => 'Defina a data inicial do leilão no formato dd/mm/AAAA.',
            'leilao_hora_inicial.required' => 'Defina a hora inicial do leilão.',
            'leilao_data_final.required' => 'Defina a data final do leilão no formato dd/mm/AAAA.',
            'leilao_hora_final.required' => 'Defina a hora final do leilão.',
            'logo.required' => 'Defina um logotipo para esse leilão.',
            'destaque.required' => 'Defina um foto de destaque para o leilão.',

        ];
    }
}
