<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class ClienteAtendimentos extends Model
{
    protected $fillable = [
        'usuario_id',
        'cadastro_id',
        'data_atendimento',
        'descricao',
    ];

    protected $dates = [
        'data_atendimento',
        'created_at',
        'updated_at'
    ];

    public function usuarioAtendimento()
    {
        $usuario = $this->belongsTo('App\User', 'usuario_id', 'codigo')->first();
        $dadosUsuario = $usuario->codigo . ' - ' . $usuario->nome;
        return $dadosUsuario;
    }

    public function encontrarUsuarioAtendimento()
    {
        $usuario = \App\User::on('mysql')->where('codigo', $this->usuario_id)->first();

        if (!$usuario) {
            $usuario = \App\User::on('mysql_2')->where('codigo', $this->usuario_id)->first();
        }

        return $usuario ? $usuario->codigo . ' - ' . $usuario->nome : 'Usuário não encontrado';
    }
}
