<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\User;
use Auth;

class LoginController extends Controller
{
    private $salt = '';
    public function __construct() {
        $this->salt = \Config::get('app.pwd_salt');
    }
    public function index()
    {
        return view('admin.login.login');
    }
    public function login(Request $request)
    {
        $recap = 'g-recaptcha-response';
        $resposta = file_get_contents("https://www.google.com/recaptcha/api/siteverify?secret=".env('RECAPTCHA_SECRET')."&response=".$request->$recap."&remoteip=".$_SERVER['REMOTE_ADDR']);
        $resposta = json_decode($resposta);
        if (!$resposta->success) {
            if (env('DISABLE_RECAPTCHA') == false) {
                return redirect()->back()->with('error_login', 'Erro ao validar o recaptcha');
            }
        }
        $email = $request->email;
        $senha = md5($request->senha);
        $user = User::where('email', $email)->first();
        if (!$user) {
            return redirect()->back()->with('error_login', 'Email e/ou senha não foram encontrados ou email não está ativo no sistema!');
        }
        if ($senha == $user->senha) {
            Auth::guard('admin')->login($user, true);
            $user->senha = \Hash::make($request->senha . $this->salt);
            $user->last_login = date('Y-m-d H:i:s');
            $user->save();
            return redirect('/leiloes/cadastro');
        }

        if (\Hash::check($request->senha . $this->salt, $user->senha)) {
            Auth::guard('admin')->login($user, true);
            $user->senha = \Hash::make($request->senha . $this->salt);
            $user->last_login = date('Y-m-d H:i:s');
            $user->save();
            return redirect('/leiloes/cadastro');
        }

        return redirect()->back()->with('error_login', 'Email e/ou senha não foram encontrados ou email não está ativo no sistema!');
    }

    public function logout() {
        Auth::guard('admin')->logout();
        return redirect('signin');
    }
}