<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Mail;
use Illuminate\Http\Request;
use App\ClienteAtendimentos;
use Auth;

class AtendimentoController extends Controller
{

    public function registrarAtendimento(Request $request, $id)
    {
        $input = $request->all();
        $model = new ClienteAtendimentos();
        $model->usuario_id = Auth::guard('admin')->user()->codigo;
        $model->cadastro_id = $id;
        $model->data_atendimento = $this->composeDataAtendimento($input);
        $model->descricao = $input['descricao'];

        if ($model->save()) {
            return response()->json(['msg' => 'ok'], 200);
        }

        return response()->json(['msg' => 'Erro ao registrar atendimento'], 500);
    }

    public function excluirAtendimento(Request $request) {
        $model = ClienteAtendimentos::find($request->atendimento_id);
        if ($model->delete()) {
            return response()->json(['msg' => 'ok'], 200);
        }

        return response()->json(['msg' => 'Erro ao excluir atendimento'], 500);
    }

    private function composeDataAtendimento($input) {
        $dataAtendimento = $input['data_atendimento'];
        $dataAtendimento = explode('/', $dataAtendimento);
        $dataAtendimento = $dataAtendimento[2] . '-' . $dataAtendimento[1] . '-' . $dataAtendimento[0];
        $horaAtendimento = $input['hora_atendimento'];
        $dataAtendimento = $dataAtendimento . ' ' . $horaAtendimento;
        return $dataAtendimento;
    }

}
