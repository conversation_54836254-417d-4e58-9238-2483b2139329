<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Routines\AberturaLeilao;

class IniciaLeiloes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'leilao:iniciar {lote?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Inicia leilões marcados como futuro';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $lote = (int) $this->argument('lote');
        AberturaLeilao::abertura($lote);
    }
}
