select * from (
                                select * from (SELECT DISTINCT A.*,<PERSON><PERSON>idleilao, categoria
                                FROM leiloes A
                                INNER JOIN lotes B ON A.codigo = B.idleilao
                                WHERE A.encerrado = 1 and <PERSON><PERSON>suspender = 2
                                -- and B.categoria in (4,5,6) and B.subcategoria not in(29,30)
                                
                                order by A.leilao2_data_final asc, categoria asc, A.leilao2_hora_final asc, desconto_p desc, B.lance_data_1 desc
                                ) as aberto

                                union all

                                select * from (SELECT DISTINCT A.*,B.idleilao, categoria
                                FROM leiloes A
                                INNER JOIN lotes B ON A.codigo = B.idleilao
                                WHERE A.encerrado = 7 and A<PERSON>suspender = 2
                                and B.categoria in (4,5,6) and B.subcategoria not in(29,30)
                                
                                order by leilao_data_inicial asc, leilao_hora_inicial asc, categoria asc, desconto_p desc, B.lance_data_1 desc
                                ) as futuro

                                union all

                                select * from (SELECT DISTINCT A.*,<PERSON><PERSON>idleilao, categoria
                                FROM leiloes A
                                INNER JOIN lotes B ON A.codigo = B.idleilao
                                WHERE A.encerrado = 7 and A.suspender = 2
                                and B.categoria in (4,5) and B.subcategoria in(29,30)
                                
                                order by leilao_data_inicial asc, leilao_hora_inicial asc, categoria asc, desconto_p desc, B.lance_data_1 desc
                                ) as futuro3

                                union all

                                select * from (SELECT DISTINCT A.*,B.idleilao, categoria
                                FROM leiloes A
                                INNER JOIN lotes B ON A.codigo = B.idleilao
                                WHERE A.encerrado = 7 and A.suspender = 2
                                and B.categoria = 7 
                                
                                order by leilao_data_inicial asc, leilao_hora_inicial asc, subcategoria asc, desconto_p desc, B.lance_data_1 desc
                                ) as futuro1
                                
                                union all

                                select * from (SELECT DISTINCT A.*,B.idleilao, categoria
                                FROM leiloes A
                                INNER JOIN lotes B ON A.codigo = B.idleilao
                                WHERE A.encerrado = 8 and A.suspender = 2
                                and B.categoria in (4,5,6)
                                
                                order by leilao2_data_final desc, leilao2_hora_final desc, categoria asc, desconto_p desc
                                ) as vendido

                                union all

                                select * from (SELECT DISTINCT A.*,B.idleilao, categoria
                                FROM leiloes A
                                INNER JOIN lotes B ON A.codigo = B.idleilao
                                WHERE A.encerrado = 8 and A.suspender = 2
                                and B.categoria = 7 
                                
                                order by leilao2_data_final desc, leilao2_hora_final desc, subcategoria asc, desconto_p desc
                                ) as vendido1

                                union all

                                select * from (SELECT DISTINCT A.*,B.idleilao, categoria
                                FROM leiloes A
                                INNER JOIN lotes B ON A.codigo = B.idleilao
                                WHERE A.encerrado = 6 and A.suspender = 2
                                and B.categoria in (4,5,6)
                                
                                order by leilao2_data_final desc, leilao2_hora_final desc, categoria asc, desconto_p desc
                                ) as encerrado

                                union all

                                select * from (SELECT DISTINCT A.*,B.idleilao, categoria
                                FROM leiloes A
                                INNER JOIN lotes B ON A.codigo = B.idleilao
                                WHERE A.encerrado = 6 and A.suspender = 2
                                and B.categoria = 7 
                                
                                order by leilao2_data_final desc, leilao2_hora_final desc, subcategoria asc, desconto_p desc
                                ) as encerrado1

                                union all

                                select * from (SELECT DISTINCT A.*,B.idleilao, categoria
                                FROM leiloes A
                                INNER JOIN lotes B ON A.codigo = B.idleilao
                                WHERE A.encerrado = 3 and A.suspender = 2
                                and B.categoria in (4,5,6)
                                
                                order by leilao2_data_final desc, leilao2_hora_final desc, categoria asc, desconto_p desc
                                ) as suspenso

                                union all

                                select * from (SELECT DISTINCT A.*,B.idleilao, categoria
                                FROM leiloes A
                                INNER JOIN lotes B ON A.codigo = B.idleilao
                                WHERE A.encerrado = 3 and A.suspender = 2
                                and B.categoria = 7 
                                
                                order by leilao2_data_final desc, leilao2_hora_final desc, subcategoria asc, desconto_p desc
                                ) as suspenso1
                                
                                union all

                                select * from (SELECT DISTINCT A.*,B.idleilao, categoria
                                FROM leiloes A
                                INNER JOIN lotes B ON A.codigo = B.idleilao
                                WHERE A.encerrado = 5 and A.suspender = 2
                                and B.categoria in (4,5,6)
                                
                                order by leilao2_data_final desc, leilao2_hora_final desc, categoria asc, desconto_p desc
                                ) as acordo

                                union all

                                select * from (SELECT DISTINCT A.*,B.idleilao, categoria
                                FROM leiloes A
                                INNER JOIN lotes B ON A.codigo = B.idleilao
                                WHERE A.encerrado = 5 and A.suspender = 2
                                and B.categoria = 7 
                                
                                order by leilao2_data_final desc, leilao2_hora_final desc, subcategoria asc, desconto_p desc
                                ) as acordo1

                                union all

                                select * from (SELECT DISTINCT A.*,B.idleilao, categoria
                                FROM leiloes A
                                INNER JOIN lotes B ON A.codigo = B.idleilao
                                WHERE A.encerrado = 2 and A.suspender = 2
                                and B.categoria in (4,5,6)
                                
                                order by leilao2_data_final desc, leilao2_hora_final desc, categoria asc, desconto_p desc
                                ) as prejudicado

                                union all

                                select * from (SELECT DISTINCT A.*,B.idleilao, categoria
                                FROM leiloes A
                                INNER JOIN lotes B ON A.codigo = B.idleilao
                                WHERE A.encerrado = 2 and A.suspender = 2
                                and B.categoria = 7 
                                
                                order by leilao2_data_final desc, leilao2_hora_final desc, subcategoria asc, desconto_p desc
                                ) as prejudicado1

                                union all

                                select * from (SELECT DISTINCT A.*,B.idleilao, categoria
                                FROM leiloes A
                                INNER JOIN lotes B ON A.codigo = B.idleilao
                                WHERE A.encerrado = 4 and A.suspender = 2
                                and B.categoria in (4,5,6)
                                
                                order by leilao2_data_final desc, leilao2_hora_final desc, categoria asc, desconto_p desc
                                ) as adjudicado

                                union all

                                select * from (SELECT DISTINCT A.*,B.idleilao, categoria
                                FROM leiloes A
                                INNER JOIN lotes B ON A.codigo = B.idleilao
                                and B.categoria = 7 
                                WHERE A.encerrado = 4 and A.suspender = 2
                                
                                order by leilao2_data_final desc, leilao2_hora_final desc, subcategoria asc, desconto_p desc
                                ) as adjudicado1
            ) as TODOS