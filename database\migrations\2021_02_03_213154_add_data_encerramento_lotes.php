<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Leiloes;
use App\Lotes;

class AddDataEncerramentoLotes extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('lotes', function (Blueprint $table) {
            $table->date('leilao_data_inicial');
            $table->date('leilao_data_final');
            $table->date('leilao2_data_inicial');	
            $table->date('leilao2_data_final');
            $table->time('leilao_hora_inicial', 0);
            $table->time('leilao_hora_final', 0);
            $table->time('leilao2_hora_inicial', 0);
            $table->time('leilao2_hora_final', 0);
        });
        $leiloes = Leiloes::all();
            
        foreach ($leiloes as $leilao) {
            Lotes::where('idleilao', (int) $leilao['codigo'])->
                update([
                    'leilao_data_inicial' => $leilao['leilao_data_inicial'],
                    'leilao_data_final' => $leilao['leilao_data_final'],
                    'leilao2_data_inicial' =>$leilao['leilao2_data_inicial'], 
                    'leilao2_data_final' => $leilao['leilao2_data_final'],
                    'leilao_hora_inicial' => $leilao['leilao_hora_inicial'],
                    'leilao_hora_final' => $leilao['leilao_hora_final'],
                    'leilao2_hora_inicial' =>$leilao['leilao2_hora_inicial'], 
                    'leilao2_hora_final' => $leilao['leilao2_hora_final'],
                ]);
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('lotes', function (Blueprint $table) {
            $table->dropColumn('leilao_data_inicial');
            $table->dropColumn('leilao_data_final');
            $table->dropColumn('leilao2_data_inicial');	
            $table->dropColumn('leilao2_data_final');
            $table->dropColumn('leilao_hora_inicial');
            $table->dropColumn('leilao_hora_final');
            $table->dropColumn('leilao2_hora_inicial');
            $table->dropColumn('leilao2_hora_final');
        });
    }
}
