<?php

namespace App\Services;

use App\Mail\CadastroUsuario;
use App\Mail\NotificaAdms;
use App\Utils\Notifications;
use Illuminate\Support\Facades\Mail;

class EnviaEmailsCadastroService {
    
    public static function enviaEmails(\App\Cadastros $cadastro, $senha ) {

        $nome = $cadastro->pessoa == 2 ? $cadastro->razao_social : $cadastro->nome;

        Mail::to($cadastro->email)
        ->queue(new CadastroUsuario($cadastro, $senha, ''));
        $texto  = 'O cliente ' . $nome . ', acaba de se cadastrar no site com o email ' . $cadastro->email . '!<br>';
        Mail::to('<EMAIL>')
        ->queue(new NotificaAdms('Novo usuário cadastrado', $texto));

        return true;
    }
}