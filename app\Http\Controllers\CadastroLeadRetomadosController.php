<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Requests\CadastroLeadRetomados;
use App\Services\EnviaEmailsLeadService;
use App\Lead;
use App\Lotes;
use Auth;

class CadastroLeadRetomadosController extends Controller
{
    /**
     * Update the profile for the given user.
     *
     * @param  int  $id
     * @return redirect()
     */
    public function save(CadastroLeadRetomados $request )
    {
        try {
            $recap = 'g-recaptcha-response';

            $resposta = file_get_contents("https://www.google.com/recaptcha/api/siteverify?secret=".env('RECAPTCHA_SECRET')."&response=".@$request->$recap."&remoteip=".$_SERVER['REMOTE_ADDR']);
            $resposta = json_decode($resposta);

            if (!$resposta->success) {
                return response()->json([
                    'status' => 'false',
                    'errors' => [
                        'recaptcha_lead' => 'Você deve marcar o recaptcha.'
                    ]
                ], 503);
            }
            if ($this->leadExiste($request->email_lead, $request->idlote)) {
                return response()->json([
                    'status' => true
                ]);
            }
            $lote = Lotes::find($request->idlote);
            $lead = new Lead;
            $dataNascimento = '';
            $dataNascimentoHumanRead = '';
            $rendaBruta = '';
            $lead->data_nascimento = '';
            $lead->renda_bruta = '';

            if (isset($request->nascimento_lead)) {
                $dataNascimentoHumanRead = $request->nascimento_lead;
                $dataNascimento = explode('/', $request->nascimento_lead);
                $dataNascimento = $dataNascimento[2] . '-' . $dataNascimento[1] . '-' . $dataNascimento[0];
                $lead->data_nascimento = $dataNascimento;
                $rendaBruta = $request->renda_lead;
                $lead->renda_bruta = str_replace(',', '.', str_replace('.', '', $request->renda_lead));
            }
            $lead->idlote = $request->idlote;
            $lead->nome = $request->nome_lead;
            $lead->cpf = $request->cpf_lead;
            $lead->email = $request->email_lead;
            $lead->telefone = $request->telefone_lead;
            $lead->forma_pagamento = $request->forma_pagamento;
            $lead->contato_whatsapp = (int) $request->contato_whats_app;
            $lead->contato_telefone = (int) $request->contato_telefone;
            $lead->contato_email = (int) $request->contato_email;
            $lead->mensagem = $request->mensagem_lead;
            $lead->save();
            EnviaEmailsLeadService::enviaEmails($lote, $lead->nome, $lead->cpf, $lead->email, $lead->telefone, $rendaBruta, $dataNascimentoHumanRead, $lead->mensagem);
            return response()->json([
                'status' => true
            ]);
        }catch(\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    private function leadExiste($email, $lote) {
        $lead = Lead::where('idlote', $lote)->where('email', trim($email))->first();
        if ($lead) {
            return true;
        }
        return false;
    }

}