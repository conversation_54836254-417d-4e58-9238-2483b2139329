<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Audit;

class AuditController extends Controller
{
    private $salt = '';
    
    /**
     * List all profiles.
     *
     * @param  int  $id
     * @return redirect()
     */
    public function index()
    {
        $audit = Audit::orderBy('id', 'desc')->limit(500)->get();
        return view('admin.audits.index', ['audits' => $audit]);
    }

    public function verEvento($id = 0) {
        $id = (int) $id;
        $usuario  = 'Evento do sistema';
        $tipoEvento = '';
        $ip = '';
        $navegador = '';
        $email = 'Usuário não logado';
        if ($id === 0) {
            return response()->json(['msg' => 'Nenhum id enviado'], 500);
        }
        $evento = Audit::where('id', $id)->first();

        if ($evento === null) {
            return response()->json(['msg' => 'Evento não encontrado'], 404);
        }

        if ($evento->user_type !== null) {
            $instance = '\\' . $evento->user_type;
            $instance = new $instance;
            $usuario = $instance->where('codigo', $evento->user_id)->first();
            $email = $usuario['email'];
        }
        $url = $evento->url === 'console' ? 'Evento iniciado via processos do sistema' : $evento->url;
        $tipoEvento = $evento->eventType();
        $data = $evento->created_at->format('d/m/Y H:i:s');
        $ip = $evento->ip_address;
        $navegador = $evento->user_agent;
        $dadosModificados = $evento->new_values;

        $array = array('user' => $email,
        'eventtype' => $tipoEvento,
        'date' => $data,
        'navigator' => $navegador,
        'url' => $url,
        'ipaddr' => $ip,
        'changeddata' => $dadosModificados,);
        return response()->json($array, 200);
    }

}
