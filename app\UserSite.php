<?php

namespace App;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use OwenIt\Auditing\Contracts\Auditable;

class UserSite extends Authenticatable implements Auditable
{
    use Notifiable;
    use \OwenIt\Auditing\Auditable;
    
    protected $connection = 'mysql_2';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
      'pessoa', 'nome', 'cpf', 'rg', 'filiacao', 'profissao', 'empregador', 'sexo', 
      'estado_civil', 'tipo_uniao', 'conjuge', 'c_cpf', 'c_rg', 'razao_social', 'cnpj', 
      'insc_estadual', 'nome_fantasia', 'faturamento', 'segmento', 'socio', 's_cpf', 's_rg', 
      'email', 'telefone', 'celular', 'cep', 'endereco', 'numero', 'complemento', 'bairro', 'cidade', 
      'estado', 'informacoes', 'como_chegou', 'status', 'senha', 'changed_password', 'last_login',
      'lead_tipo_imovel','regiao_interesse','lead_faixa_valor',
      'investimento','uso_proprio','completar_cadastro',
    ];

    protected $table = 'cadastros';
    protected $primaryKey = 'codigo';
    
    public $timestamps = false; 

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        //'nome', 'telefone', 'email', 'tipo', 'senha',
        'senha'
    ];

    public function docPessoal()
    {
        return $this->hasOne('App\CadastrosDocumentos', 'idcadastro', 'codigo')
               ->latest('codigo');
    }

}