<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>CK<PERSON>inder vartotojo instrukcija</title>
	<link href="../../files/other/help.css" type="text/css" rel="stylesheet" />
	<script type="text/javascript" src="../../files/other/help.js"></script>
	<meta name="robots" content="noindex, nofollow" />
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" /></head>
<body>
	<h1>
		Failų įkėlimo mygtukas
	</h1>
	<p>
		Įkėlimo* mygtukas, esantis <a href="005.html">įrankių juostoje</a>, atidaro "Failų įkėlimą",
		kuris skirtas naujų failų įkėlimui į segtuvą. Žemiau pateiktas šio įrankio atvaizdas:</p>
	<p style="text-align: center">
		<img height="153" src="images/012.gif" width="404" />&nbsp;</p>
	<p>
		Kad uždarytumėte failų įkėlimą, spustelėkitee "Atšaukti" mygtuką, arba spauskite "Įkelti" iš naujo
		įrankių juostoje.</p>
	<p>
		* "<strong>Įkelti/Upload</strong>" tai yra techninis terminas. Tai reiškia failo perdavimą
		iš savo kompiuterio į serverį.</p>
	<h2>
		Įkėlimo etapai</h2>
	<ol>
		<li>Pasirinkite failą iš savo kompiuterio paspausdami "Browse/Pasirinkti..." mygtuką. Tekstas
			mygtuke skiriasi priklausomai nuo naršyklės, bet jis visada bus po mygtuku
			"Pasirinkite, kokį failą norėtumėte įkelti"</li>
		<li>Tada paspauskite "Įkelti pasirinktą failą" mygtuką. Atsiras pranešimas, pranešantis apie
			įkeliamo failo progresą.</li>
		<li>Palaukite kol įkėlimas pasibaigs. Tuomet failų įkėlimko skydelis iš kart užsidarys,
			ir įkeltas failas bus iš kart pasirinktas <a href="004.html">Failų valdyme</a>.</li>
	</ol>
	<h2>
		Įkėlimo pranešimai</h2>
	<p>
		Žemiau pateikti pranešimai gali atsirasti įkeliant failus:</p>
	<h3>
		Failas, su tuo pačiu pavadinimu jau egzistuoja. Įkeltas failas buvo pervadintas
		į "failopavadinimas(1).plėtinys"</h3>
	<p>
		Tai parodo jof failas su tokiu pačiu pavadinimu jau egzistuoja tame pačiame segtuve.
		Konfliktui išvengti, prie jo pavadinimo pridedamas "(1)".
        </p>
	<h3>
		Neteisingas failas</h3>
	<p>
		Įkeliamas failas buvo nepriimtas.
	</p>
	<p>
		Dažniausia šių pranešimų priežastis yra ta, kad CKFinder buvo sukonfigūruotas
		nepriimtų tokios rūšies failų, atsižvelgiant į plėtinius. Tai saugumo apribojimas.
		Kita priežastis gali būti ta, kad failo apimtis yra per didelė.
		Tokiu atveju, reikia pakeisti pačio serverio nustatymus, kurie leistų įkelti
        didesnės apimties failus.
	</p>
	<h3>Įkeliamas failas buvo atšauktas. Faile yra HTML duomenys.</h3>
	<p>Įkeliamame faile yra html duomenys. Saugumo sumetimais, įkelti galima tik tuos failus,
    kurių plėtiniai yra nurodyti konfigūracijoje ir kurie neturi html plėtinio.</p>

	<p>
		Dėl detalesnių pakeitimų prašau susisiekite su savo sistemų administratoriais.</p>
</body>
</html>
