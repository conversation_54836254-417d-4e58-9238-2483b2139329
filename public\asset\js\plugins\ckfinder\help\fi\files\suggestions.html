<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>CK<PERSON>inder k<PERSON>ytt<PERSON>ohje</title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<link href="../../files/other/help.css" type="text/css" rel="stylesheet" />
	<script type="text/javascript" src="../../files/other/help.js"></script>
	<meta name="robots" content="noindex, nofollow" />
	<script type="text/javascript">

function CheckForm()
{
	if ( document.getElementById( 'xSuggestion' ).value == '' )
	{
		alert( 'Kir<PERSON><PERSON> ehdotuksesi ennen viestin llähettämistä!' ) ;
		return false ;
	}

	document.getElementById( 'xSubmit' ).disabled = true ;
	document.getElementById( 'spamcheck' ).value = 9945 + 13671 ;

	return true ;
}

	</script>
</head>
<body>
	<h1>
		Kehitysehdotuksesi
	</h1>
	<p>
		<a href="http://cksource.com/contact">Lähetä ehdotuksesi</a> dokumentaation kehittämiseksi.
		Parannamme mielellämme sovelluksiamme ja niiden käyttöohjeita.</p>
</body>
</html>
