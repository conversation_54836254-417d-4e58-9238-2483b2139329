<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>Uživatelská příručka CKFinder</title>
	<link href="../../files/other/help.css" type="text/css" rel="stylesheet" />
	<script type="text/javascript" src="../../files/other/help.js"></script>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta name="robots" content="noindex, nofollow" />
</head>
<body>
	<h1>
		Lišta stavu</h1>
	<p>
		<strong>Lišta stavu (Status Bar)</strong> je umístěna v dolní části rozhraní <PERSON>,
		k<PERSON><PERSON> zobrazuje informace o zvoleném souboru, celkov<PERSON> počet
		souborů ve složce, atd.</p>
	<p>
		Pokud je v CKFinder vybrán soubor, <strong>Lišta stavu</strong> zobrazí
		podrobné informace o tomto souboru, včetně jeho názvu, velikosti a
		data jeho poslední změny. Například:</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_status_file.png" width="187" height="39" alt="Lišta stavu CKFinder s vybraným souborem" />&nbsp;</p>
	<p>
		Pokud nejsou vybrány žádné soubory, bude místo toho v <strong>Liště stavu</strong> zobrazen
		celkový počet souborů v současné složce. Například:</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_status_folder.png" width="187" height="39" alt="Lišta stavu CKFinder bez vybraného souboru" />&nbsp;</p>
	<p>
		Pokud je složka prázdná, <strong>Lišta stavu</strong> zobrazí odpovídající
		zprávu. Například:</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_status_empty.png" width="187" height="39" alt="Lišta stavu CKFinder v prázdné složce" />&nbsp;</p>
</body>
</html>
