<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Lotes;

class ChangeMetragemOnLotes extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $lotes = Lotes::all();
        $lotes = $lotes->toArray();
        Schema::table('lotes', function (Blueprint $table) {
            $table->dropColumn('metragem');
        });

        Schema::table('lotes', function (Blueprint $table) {
            $table->string('metragem', 20);
        });

        foreach ($lotes as $lote) {
            $l = Lotes::find($lote['codigo']);
            $l->metragem = $lote['metragem'];
            $l->save();
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
