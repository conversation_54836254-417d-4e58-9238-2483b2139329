<?php

namespace App\Http\Controllers;

use App\Http\Requests\Portals;
use App\Portal;

class PortalsController extends Controller
{
    /**
     * List all databases.
     *
     * @return view()
     */
    public function index()
    {
        $portals = Portal::all();
        return view('admin.portais.index', ['portals' => $portals]);
    }

    public function save(Portals $request) 
    {
        try {
            $input = $request->validated();
            Portal::create($input);
            return redirect()->back()->with('success' , 'Operação concluída.');
        }catch(\Exception $e) {
            return redirect()->back()->with('error' , 'Houve uma falha ao executar a operação. Tente novamente.');
        }
    }
}
