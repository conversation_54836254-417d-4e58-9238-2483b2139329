<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Lotes;
use App\Leiloes;
use App\Lances;
use App\LanceAutomatico;
use App\Jobs\LanceAutomatico as LanceAutomaticoJob;
use App\LancesParcelados;
use Illuminate\Support\Facades\Mail;
use App\Mail\ConfirmaLance;
use App\Routines\LanceRoutine;
use Auth;

class LancesController extends Controller
{
    public function verLances($idlote)
    {
        $lote = Lotes::findOrFail($idlote);

        $titulo = $lote->titulo;

        $lancesParcelados = LancesParcelados::selectRaw('"2" as tipo, codigo as id, idcadastro, idlote, valor, data_lance, usuario_desativa, desativado, data_desativa, automatico')->where('idlote', $idlote)->orderBy('valor', 'desc');

        $lances = Lances::selectRaw('"1" as tipo, codigo as id, idcadastro, idlote, valor, data_lance, usuario_desativa, desativado, data_desativa, automatico')->union($lancesParcelados)->where('idlote', $idlote)->orderBy('tipo', 'asc')->orderBy('valor', 'desc')->get();

        $maiorLance = $lances->first();


        return view('admin.lances.lances', ['maiorLance' => $maiorLance,
                                            'lances' => $lances,
                                           'titulo' => $titulo,
                                           'idlote' => $idlote]);

    }

    public function detalheLance($id) {
        $lance = LancesParcelados::find($id);
        return response()->json(['ok' =>  true, 'lance' => $lance, 'html' => view('admin.lances.modal-lance-detail', ['lance' => $lance])->render()]);

    }
    /**
     * Exclui um lance
     */
    public function excluirLance($id) {
        $lance = Lances::findOrFail($id);
        $lance->delete();
        return redirect()->back();
    }

    public function desativarLance(Request $request) {
        $user = Auth::guard('admin')->user();
        $id = (int) $request->id;
        $class = Lances::class;
        if ($request->tipo == '2') {
            $class = LancesParcelados::class;
        }
        $lance = $class::findOrFail($id);
        $lote = Lotes::findOrFail($lance->idlote);
        if ($lote->encerrado == 8) {
            return response()->json(['status' => true, 'msg' => 'Este leilão está encerrado. Não é possível excluir o lance.']);
        }

        $lance->obs = $request->obs;
        $lance->desativado = 1;
        $lance->data_desativa = date('Y-m-d H:i:s');
        $lance->usuario_desativa = $user->nome;
        $lance->save();
        return response()->json(['status' => true, 'msg' => 'ok']);
    }

    /**
     * Exclui um lance parcelado
     */
    public function excluirLanceParc($id) {
        $lance = LancesParcelados::findOrFail($id);
        $lance->delete();
        return redirect()->back();
    }
    /**
     * Declara um lance parcelado como vencedor
     */
    public function declararVencedor($id)
    {
        $lance = LancesParcelados::findOrFail($id);
        //atualiza tudo para 0
        LancesParcelados::where('idlote', $lance->idlote)->update(['status' => 0]);
        // segue marcando o último como escolhido
        $lance->status = '1';
        $lance->save();
        $lote = Lotes::findOrFail($lance->idlote);
        $lote->encerrado = 8;
        $lote->save();
        $leilao = Leiloes::findOrFail($lote->idleilao);
        $leilao->encerrado = 8;
        $leilao->save();
        return redirect('/lances/lote/' . $lance->idlote)->with(['success' => 'Arrematante selecionado com sucesso.' ]);
    }

    public function addLance(Request $request, $codigo) {
        $codigo = (int) $codigo;
        $leilao = $request->get('leilao');
        $lote = $request->get('lote');

        $redirect = redirectLocal($lote);

        $user = Auth::user();
        $lanceRoutine = new LanceRoutine();

        $leilaoInfo = new \StdClass();

        $leilaoInfo->leilao = $request->get('leilao');
        $leilaoInfo->lote = $request->get('lote');
        $leilaoInfo->valorLance = $request->get('valorLance');

        $lanceRoutine->addLance($leilaoInfo, $codigo, $user->codigo);

        $codReferencia = '';

        if ($leilaoInfo->leilao) {
            $codReferencia = trim($leilaoInfo->leilao->cod_referencia);
            if ($codReferencia != '') {
                $codReferencia = $codReferencia . ' - ';
            }
        }

        Mail::to($user->email)
        ->queue(new ConfirmaLance($user, '', 'Confirmamos seu lance!', $leilaoInfo->valorLance, $lote->titulo));

        Mail::to('<EMAIL>')
        ->cc(getAdmEmails())
        ->queue(new ConfirmaLance($user, 'layout.email.confirma-lance-adm', 'Novo lance enviado!', $leilaoInfo->valorLance, $codReferencia . $lote->titulo));

        $lanceAutomatico = new LanceAutomatico();
        $temLanceAutomatico = $lanceAutomatico->where('idcadastro', $user->codigo)->where('idlote', $codigo)->get();
        $permiteLanceAutomatico = true;

        if ($temLanceAutomatico->count() > 0) {
            $permiteLanceAutomatico = false;
        }

        if ($request->ajax())  {
            return ['msg' => 'Lance efetuado com sucesso!', 'lote' => $codigo, 'lance_automatico' => $permiteLanceAutomatico];
        }

        return redirect($redirect)->with(['success' => 'Lance efetuado com sucesso!', 'lance_automatico' => $permiteLanceAutomatico]);
    }

    public function defineLanceAutomatico(Request $request) {
        $user = Auth::user();
        $valor = $request->limite_auto;
        $valor = str_replace(",", ".", str_replace(".", "", $valor));
        $lote = (int) $request->lote;
        $lanceAutomatico = new LanceAutomatico();
        $lanceAutomatico->valor = $valor;
        $lanceAutomatico->idlote = $lote;
        $lanceAutomatico->idcadastro = $user->codigo;
        $lanceAutomatico->save();
        $redirect = redirectLocal(Lotes::find($lote));

        $l = Lances::where('idlote', $lote)->orderBy('codigo', 'desc')->first();
        if ($l->idcadastro != $user->codigo) {
            $lote = Lotes::with('leilao')->find($lote);
            $leilaoInfo = new \StdClass();
            $leilaoInfo->leilao = $lote['leilao']->codigo;
            $leilaoInfo->lote = $lote;
            $leilaoInfo->valorLance = $l->valor + $lote->incremento;
            $lanceRoutine = new LanceRoutine();
            $lanceRoutine->addLance($leilaoInfo, $lote->codigo, $user->codigo);
        }
        if ($request->ajax())  {
            return ['msg' => 'Você configurou com sucesso lances automáticos para este leilão!', 'lote' => $lote];
        }
        return redirect($redirect)->with('success', 'Você configurou com sucesso lances automáticos para este leilão!');
    }
}
