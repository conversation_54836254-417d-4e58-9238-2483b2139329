<?php

namespace App\Http\Controllers;

use App\Lotes;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class HabilitadosLoteController extends Controller
{
    /**
     * List all profiles.
     *
     * @param  int  $id
     * @return redirect()
     */
    public function index()
    {
        $vet = Lotes::join('leiloes', 'leiloes.codigo', '=', 'lotes.idleilao')->distinct()->get(['cod_referencia', 'lotes.codigo', 'lotes.titulo']);
        return view('admin.habilitados_lotes.index', ['vet' => $vet,]);
    }

    public function search(Request $request)
    {
        $input = $request->all();

        $vet = Lotes::join('leiloes', 'leiloes.codigo', '=', 'lotes.idleilao')
            ->distinct()
            ->get(['cod_referencia', 'lotes.codigo', 'lotes.titulo']);

        $lote = Lotes::with('listaHabilitados')->find($input['idlote']);

        $habilitados = [];
        if ($lote) {
            $idCadastros = $lote->listaHabilitados->pluck('idcadastro')->unique()->toArray();

            if (!empty($idCadastros)) {
                $cadastros = \DB::connection('mysql_2')
                    ->table('cadastros')
                    ->whereIn('codigo', $idCadastros)
                    ->get()
                    ->keyBy('codigo');

                foreach ($lote->listaHabilitados as $item) {
                    if (isset($cadastros[$item->idcadastro])) {
                        $cadastro = $cadastros[$item->idcadastro];

                        $habilitados[] = [
                            'data_habilitacao' => $item->data_habilitacao,
                            'nome' => $cadastro->nome,
                            'razao_social' => $cadastro->razao_social
                        ];
                    }
                }
            }
        }

        return view('admin.habilitados_lotes.index', [
            'vet' => $vet,
            'idlote' => $input['idlote'],
            'habilitados' => $habilitados
        ]);
    }

    public function verHabilitados($idlote)
    {
        $lote = Lotes::with('listaHabilitados')->find($idlote);

        $habilitados = [];
        if ($lote) {
            $idCadastros = $lote->listaHabilitados->pluck('idcadastro')->unique()->toArray();

            if (!empty($idCadastros)) {
                $cadastros = \DB::connection('mysql_2')
                    ->table('cadastros')
                    ->whereIn('codigo', $idCadastros)
                    ->get()
                    ->keyBy('codigo');

                foreach ($lote->listaHabilitados as $item) {
                    if (isset($cadastros[$item->idcadastro])) {
                        $cadastro = $cadastros[$item->idcadastro];

                        $habilitados[] = [
                            'data_habilitacao' => $item->data_habilitacao,
                            'nome' => $cadastro->nome,
                            'razao_social' => $cadastro->razao_social,
                            'email' => $cadastro->email,
                            'apelido' => $cadastro->apelido
                        ];
                    }
                }
            }
        }

        $titulo = $lote["titulo"] ?? '';

        return view('admin.habilitados_lotes.lotes-habilitados', [
            'titulo' => $titulo,
            'habilitados' => $habilitados
        ]);
    }
}
