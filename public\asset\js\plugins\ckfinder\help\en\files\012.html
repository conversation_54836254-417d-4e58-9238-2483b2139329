<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>CKFinder User's Guide</title>
	<link href="../../files/other/help.css" type="text/css" rel="stylesheet" />
	<script type="text/javascript" src="../../files/other/help.js"></script>
	<meta name="robots" content="noindex, nofollow" />
</head>
<body>
	<h1>
		Context Menu</h1>
	<p>
		The <strong>Context Menu</strong> is a pop-up menu that appears whenever you
		click a file or a folder inside the CKFinder interface with the right mouse
		button, use the <em>Menu/Application</em> key on your keyboard, or the
		<em>(Ctrl+)Shift+F10</em> keyboard shortcut. It gives you access to file browser
		operations that are available for a given type of object.</p>
	<p>The context menu can also be opened by clicking the down arrow icon
		(<img src="../../files/images/CKFinder_menu_arrow.png" width="9" height="7" alt="" />)
		that is available in some environments or in mobile browsers next the name of the
		active folder or in the file boxes, as visible in the figure below.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_menu_arrows.png" width="469" height="251" alt="CKFinder context menu with helper arrows" />&nbsp;</p>
	<p>Each context menu consists of a series of options (commands) that can be
		selected in order to perform a specific operation that they are associated
		with.</p>
	<p>
		<span class="info">Note:</span> Some context menu options may be disabled (and
		thus grayed out), depending on CKFinder settings enforced by your system
		administrator.</p>
	<h2>
		Using the Context Menu</h2>
	<p>
		In order to perform an operation listed in the context menu, click it with the
		left mouse button. You can also move up and down the context menu with the
		<em>Up</em> and <em>Down Arrow</em> keys or the <em>Shift+Tab</em> and
		<em>Tab</em> combinations. Once an option is highlighted, you can activate
		it with the <em>Space</em> or <em>Enter</em> button. If an option is grayed out,
		it is unavailable unless some pre-conditions are met (e.g. you have system
		permissions to make specific changes to a file or folder).</p>
	<h2>
		Available Menus</h2>
	<p>
		The menu is context-sensitive which means that the options displayed in it
		depend on the object that you select. The following are the menus that you may
		encounter while working with a standard CKFinder installation.</p>
	<h3>
		Folder Context Menu</h3>
	<p>
		It appears when you click a folder in the <strong><a href="003.html">Folders Pane</a>
		</strong> with the right mouse button (or use the keyboard shortcuts described
		above):</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_folder_menu.png" width="148" height="128" alt="CKFinder folder context menu" />&nbsp;</p>
	<h3>
		File Context Menu</h3>
	<p>
		It appears when you click a file in the <strong><a href="004.html">Files Pane</a>
		</strong> with the right mouse button (or use the keyboard shortcuts described
		above):</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_file_menu.png" width="130" height="156" alt="CKFinder file context menu" />&nbsp;</p>
</body>
</html>
