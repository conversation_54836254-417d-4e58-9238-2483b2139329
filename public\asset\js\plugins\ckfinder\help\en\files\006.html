<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>CKFinder User's Guide</title>
	<link href="../../files/other/help.css" type="text/css" rel="stylesheet" />
	<script type="text/javascript" src="../../files/other/help.js"></script>
	<meta name="robots" content="noindex, nofollow" />
</head>
<body>
	<h1>
		Upload Button</h1>
	<p>
		The <strong>Upload</strong> button that is available in the CKFinder
		<strong><a href="005.html">Toolbar</a></strong> opens the
		<strong>Upload Pane</strong> which you can use to add new files to the
		current folder.</p>
	<p>The figure below presents the default file browser <strong>Upload Pane</strong>
		that is expanded when you click the toolbar button.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_upload_01.png" width="591" height="145" alt="Flash Upload Panel in CKFinder" />&nbsp;</p>
	<p>
		In order to close (collapse) the <strong>Upload Pane</strong>, press
		the <strong>Cancel</strong> button (if you have not started the upload process yet)
		or the <strong>Close</strong> button (if the upload has already finished). You can
		also click the <strong>Upload</strong> toolbar button once again to collapse the panel.</p>
	<p>
		<span class="info">Note:</span> <em>Upload</em> is a technical term that
		stands for the action of sending the files from your local computer to a central
		computer (also known as a <em>server</em>).</p>
	<h2>
		Upload Pane Versions</h2>
	<p>CKFinder <strong>Upload Pane</strong> comes in two flavors, depending on your
		environment. By default, the multiple upload solution (that lets you send more
		files to the server in one operation) is used for all environments that support
		Adobe Flash. If, however, your local system does not support Flash, you will
		still be able to upload individual files in separate operations thanks to the
		fallback solution.</p>
	<p>The figure above presents the default <strong>Upload Pane</strong> with multiple
		upload solution enabled. For all systems that do not support Flash (including
		some mobile browsers) the following <strong>Upload Pane</strong> format will
		be used.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_upload_09.png" width="572" height="143" alt="Single file Upload Panel in CKFinder" />&nbsp;</p>
	<h2>
		Multiple File Upload</h2>
	<p>
		By default CKFinder will allow you to upload multiple files at the same time.
		In order to send the files to the server, follow the steps described below.</p>
	<p><em>Step 1:</em> Click the <strong>Upload</strong> toolbar button to open the
		<strong>Upload Pane</strong>.</p>
	<p><em>Step 2:</em> When the <strong>Upload Pane</strong> expands, choose the
		<strong>Add Files</strong> button. A native file selection dialog window of
		your operating system that opens will let you choose the local file to be
		uploaded to the server.</p>
	<p><span class="info">Note:</span> You can choose as many files as you want by
		selecting them in the dialog window all at once.</p>
	<p><em>Step 3:</em> The local file(s) will now be added to the upload queue.
		If you want to add further files to this upload batch, click the
		<strong>Add Files</strong> button again and repeat the steps described above.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_upload_03.png" width="590" height="283" alt="Upload queue in CKFinder" />&nbsp;</p>
	<p><em>Step 4:</em> If you change your mind about the files that should be sent to the server, you
		can always either remove individual files from the upload queue by clicking
		the <strong>Remove</strong> button next to the file, or decide to cancel the whole
		upload process by choosing the <strong>Cancel</strong> button of the <strong>
		Upload Pane</strong>.</p>
	<p><em>Step 5:</em> If you are ready with the file selection process, you can click the
		<strong>Upload</strong> button to start the upload. The progress of the whole process
		as well as individual files can be observed on the screen.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_upload_04.png" width="575" height="289" alt="Multiple file upload progress" />&nbsp;</p>
	<p><em>Step 6:</em> Wait for the upload to terminate. Once it is completed, the folder
		content will refresh and the uploaded file (or the last one of the multiple files)
		will be selected in the <a href="004.html"><strong>Files Pane</strong></a>.</p>
	<h2>
		Single File Upload</h2>
	<p>When Adobe Flash is not available in your system, CKFinder upload will be fully
		functional, though limited to uploading one file at a time. In order to send the
		file to the server, follow the steps described below.</p>
	<p><em>Step 1:</em> Click the <strong>Upload</strong> toolbar button to open the
		<strong>Upload Pane</strong>.</p>
	<p><em>Step 2:</em> When the <strong>Upload Pane</strong> expands, choose the
		<strong>Browse</strong> button.	A native file selection dialog window of your
		operating system that opens will let you choose the local file to be uploaded
		to the server.</p>
	<p><span class="info">Note:</span> The button caption may differ between browsers.</p>
	<p><em>Step 3:</em> The local file will now be selected. You can only upload one file
		at a time.</p>
	<p><em>Step 4:</em> If you are ready with the file selection process, you can click the
		<strong>Upload Selected File</strong> button to start the upload. The progress of the
		file upload process can be observed on the screen.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_upload_10.png" width="574" height="172" alt="Single file upload in CKFinder" />&nbsp;</p>
	<p><em>Step 5:</em> Wait for the upload to terminate. Once it is completed, the <strong>
		Upload Pane</strong> will be closed, the folder content will refresh, and the uploaded
		 file will be selected in the <a href="004.html"><strong>Files Pane</strong></a>.</p>
	<h2>
		Upload Errors</h2>
	<p>
		The following error messages  may appear when uploading files.</p>
	<h3>
		A file with the same name is already available. The uploaded file was renamed
		to "<em>filename(1).ext</em>"</h3>
	<p>
		This message indicates that the uploaded file name is already in use by another
		file in the same folder. To avoid conflict, a consecutive number, the "(1)", was
		appended to the original name.</p>
	<h3>
		Invalid file</h3>
	<p>
		The file that you attempted to upload was not accepted.
	</p>
	<p>
		The most common cause for this message is that CKFinder was configured to not
		accept the kind of file you are trying to upload based on its extension. This
		is a security restriction. It is also possible that the file size is too
		large for your system. If this is the case, the server must be configured to
		accept bigger files.
	</p>
	<h3>Upload cancelled for security reasons. The file contains HTML-like data.</h3>
	<p>The uploaded file contains HTML code. For security reasons, only files with selected
		extensions are allowed to contain HTML code.</p>
	<p>
		Please contact your system administrator to get more information regarding the
		accepted file types and their size limits.</p>
</body>
</html>
