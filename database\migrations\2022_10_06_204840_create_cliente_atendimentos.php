<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateClienteAtendimentos extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('cliente_atendimentos', function (Blueprint $table) {
            $table->id();
            $table->integer('usuario_id');
            $table->integer('cadastro_id');
            $table->dateTime('data_atendimento');
            $table->longText('descricao');
            $table->timestamps();

            $table->index(['cadastro_id']);
            $table->index(['usuario_id']);
            $table->index(['data_atendimento']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('cliente_atendimentos');
    }
}
