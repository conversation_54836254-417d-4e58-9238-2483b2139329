<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

use App\Lotes;
use App\Leiloes;

class ImportaPlanilhaLotes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lotes:importar';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Importa planihas com lotes';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $row = 0;
        $linha = 0;
        $erros = [];
        if (($handle = fopen("/tmp/lotes.csv", "r")) !== FALSE) {
            $ocorrencia = 0;
            while (($data = fgetcsv($handle, 2048, "|")) !== FALSE) {
                if ($row == 0) {
                    $row ++;
                    continue;
                }


               try {
                    $lote = $this->composeArrayLote($data, 766);
                    $numeroLote = (int) $lote['numero_lote'];
                    if ($numeroLote == 0) {

                        $numeroLote = (int) $lote['numero_lote'];
                    }
                    $loteInstance = Lotes::where('idcidade', $lote['idcidade'])->where('idestado', $lote['idestado'])
                                            ->where('idleilao', 766)->where('numero_lote', $numeroLote)->first();

                    if (!$loteInstance || $numeroLote == 0)  {
                        $loteInstance = new Lotes;
                    }
                    if ($loteInstance->idcidade == '1483' && $loteInstance->idestado == '13') {
                        $ocorrencia++;
                    }

		            foreach ($lote as $field => $value) {
                        if ($field == 'numero_lote_anterior') {
                            continue;
                        }
                        $loteInstance->$field = $value;
                    }
		            $loteInstance->save();
                    echo $numeroLote.chr(10);

                }catch(\Exception $e) {
                    $erros[$linha] = $e;
                }


            }
            fclose($handle);
        }

        dd($erros);
    }

    function composeArrayLote($data, $idLeilao) {
        $cidade = null;
        $estado =  null;
        try {
            $estadoDb = \App\Estados::where('uf', 'like', '%' . trim($data[4]) . '%')->first();
            $cidadeEstado =  \App\Cidades::where('nome', 'like', '%' . trim($data[5]) . '%')->where('idestado', $estadoDb->codigo)->first();
        }catch(\Exception $e) {
            dd($data);
            dd($estadoDb);
        }
       // dd($cidadeEstado);
        if ($cidadeEstado) {
            $cidade = $cidadeEstado->codigo;
            $estado = $cidadeEstado->idestado;
        }

        $array = [];
        $data[16] = str_replace(',', '.', str_replace(['R$ ', '.', ], '', trim($data[16])));
        $data[17] = str_replace(',', '.', str_replace(['R$ ', '.', ], '', trim($data[17])));
        $data[19] = str_replace(',', '.', str_replace(['R$ ', '.', ], '', trim($data[19])));

        $data[20] = str_replace(',', '.', str_replace(['R$ ', '.', ], '', trim($data[20])));
        $data[21] = str_replace(',', '.', str_replace(['R$ ', '.', ], '', trim($data[21])));

        $array['lance_data_1'] = $data[20];
        $array['lance_data_2'] = $data[20];
        $array['incremento'] = $data[21];
        $array['idleilao'] = $idLeilao;
        $array['categoria'] = $this->retornaCategpria($data[2]);
        $array['subcategoria'] = $this->retornaIdSubCategoria($data[3]);
        $array['idestado'] = $estado;
        $array['idcidade'] = $cidade;
        $array['bairro'] = $data[10];
        $numeroLote = explode('-', $data[13]);
        $numeroLote = $numeroLote[0];
        $numeroLote = str_replace('LOTE ', '', $numeroLote);
        $array['numero_lote'] = (int) $data[50];
        $array['numero_lote_anterior'] = (int) str_ireplace('Lote ', '', mb_strtolower($data[51]));
        $array['endereco'] = $data[11];
        $array['numero'] = $data[12];
        $array['titulo'] = $data[13];
        $array['subtitulo'] = $data[14];
        $array['avaliacao'] = $data[16];
        $array['min_venda'] = $data[17];
        $array['debitos'] = $data[18];
        //$array['abertura'] = '0000-00-00 00:00:00';
        //$array['fechamento'] = '0000-00-00 00:00:00';
        $array['num_processo'] = $data[22];
        $array['url_consulta'] = $data[23];
        $array['comissao_leiloeiro'] = (int) $data[24];
        $array['situacao'] = mb_strtolower(trim($data[15])) == 'ocupado' ? 1 : 2;
        $array['metragem'] = str_replace('m²', '', $data[6]);
        $array['dormitorios'] = $data[7];
        $array['garagem'] = $data[8];
        $array['banheiros'] = $data[9];
        $array['mapa'] = null;//$data[47];
        $array['publicacao'] = $data[25];
        $array['doe'] = $data[26];
        $array['cda'] = $data[27];
        $array['chb'] = $data[28];
        $array['ind_parcelada'] = mb_strtolower(trim($data[49])) == 'sim' ? 1 : 2;
        $array['descricao'] = $this->descricao($data);
        $array['desc_condicao_pagamento'] = str_replace(["\n", "\r\n"], "<br /> <br />", $data[44]);
        $array['visitacao'] = str_replace(["\n", "\r\n"], "<br />", $data[45]);
        $data[46] = mb_strtolower($data[46]);
        $data[47] = mb_strtolower($data[47]);
        $array['lote_destaque'] = ($data[46] == 'não' ? 0 : ($data[46] == 'sim' ? 1 : 0));
        $array['ind_parcelada'] = ($data[47] == 'não' ? 0 : ($data[47] == 'sim' ? 1 : 0));
        //$array['encerrado'] = 7; //$data[9];

        return $array;
    }

    function descricao($data) {
        $data[30] = trim ($data[30]);
        $texto = '';
        if ($data[30] != '') {
            $texto = '<p><strong>' . $data[30] . '</strong></p> <br /> <br />';
        }
        $texto .= '<p><strong>Código do leilão: </strong>' . $data[31] . '</p><br /><br />';
        $texto .= '<p><strong>Descrição do imóvel: </strong>' . $data[32] . '</p><br /><br />';
        $data[34] = trim($data[34]);
        $data[35] = trim($data[35]);
        $data[36] = trim($data[36]);
        $data[37] = trim($data[37]);
        $data[38] = trim($data[38]);
        $data[39] = trim($data[39]);
        $data[40] = trim($data[40]);
        $data[41] = trim($data[41]);
        $data[46] = trim($data[46]);
        $avaliacao = '';
        $avaliacaoAtualizado = '';
        $debTributario = '';
        $debExequendo = '';
        $valorIPTU = '';
        $condominio = '';
        $financiamento = '';
        $fgts = '';
        if (trim($data[34]) != '' && $data[34] != '0') {
            $avaliacao = '<p><strong>Valor de Avaliação: </strong>' . $data[34] . '</p>';
        }
        if (trim($data[35]) != '' && $data[35] != '0') {
            $avaliacao = '<p><strong>Valor de Avaliação Atualizado: </strong>' . $data[35] . '</p>';
        }
        if (trim($data[36]) != '' && $data[36] != '0') {
            $debTributario = '<p><strong>Débitos Tributários: </strong>' . $data[36] . '</p>';
        }

        if (trim($data[37]) != '' && $data[37] != '0') {
            $debExequendo = '<p><strong>Débitos Exequendo: </strong>' . $data[37] . '</p>';
        }

        if (trim($data[38]) != '' && $data[38] != '0') {
            $valorIPTU = '<p><strong>Valor IPTU: </strong>' . $data[38] . '</p>';
        }

        if (trim($data[39]) != '' && $data[39] != '0') {
            $condominio = '<p><strong>Débitos Condominiais: </strong>' . $data[39] . '</p>';
        }

        if (trim($data[40]) != '' && $data[40] != '0') {
            $financiamento = '<p><strong>Aceita Financiamento: </strong>' . $data[40] . '</p>';
        }

        if (trim($data[41]) != '' && $data[41] != '') {
            $fgts = '<p><strong>Aceita FGTS: </strong>' . $data[41] . '</p>';
        }

        $texto .= $avaliacao . $avaliacaoAtualizado . $debTributario . $debExequendo . $valorIPTU . $condominio . $financiamento . $fgts;

        if (trim($data[46]) != '') {
            $texto .= '<p><strong>Compra parcelada: </strong>' . str_replace(["\n", "\r\n"], "<br />", $data[46]) . '</p>';
        }

        $data[33] = trim ($data[33]);

        if ($data[33] != '') {
            $texto .= '<br /> <br /><p><strong>Observação 1: </strong> <br />' . $data[33] . '</p>';
        }

        $data[42] = trim($data[42]);

        if ($data[42] != '') {
            $texto .= '<br /> <br /><p><strong>Observação 2: </strong><br />' . $data[42] . '</p>';
        }

        $data[43] = trim($data[43]);

        if ($data[43] != '') {
            $texto .= '<br /><br /><p><strong>Informativos: </strong><br />' . str_replace(["\n", "\r\n"], "<br />", $data[43]) . '</p>';
        }

        return $texto;
    }

    function retornaCategpria(String $categoria ) {
        $categoria = trim (mb_strtolower($categoria));
        $idCategoria = 0;
        switch ($categoria) {
            case 'rurais':
            case 'rural':
                $idCategoria = 6;
            break;
            case 'residenciais':
            case 'residencial':
                $idCategoria = 4;
            break;
            case 'comerciais':
            case 'comercial':
                $idCategoria = 5;
            break;
            default:
                $idCategoria = 7;
            break;
        }
        return $idCategoria;
    }


    function retornaIdSubCategoria(String $subcategoria) {
        $subcategoria = trim (mb_strtolower($subcategoria));
        $idSubcategoria = 0;
        switch($subcategoria) {
            case 'glebas':
                $idSubcategoria = 23;
            case 'gleba':
            break;
            case 'fazenda':
            case 'fazendas':
                $idSubcategoria = 26;
            break;
            case 'chácara':
            case 'chácaras':
            case 'chacara':
            case 'chacaras':
                $idSubcategoria = 27;
            break;
            case 'sítio':
            case 'sitio':
            case 'sítios':
            case 'sitios':
                $idSubcategoria = 32;
            break;
            case 'apartamento':
            case 'apartamentos':
                $idSubcategoria = 18;
                break;
            case 'cobertura':
            case 'coberturas':
                $idSubcategoria = 18;
                break;
            case 'casa/sobrado':
            case 'casa':
            case 'sobrado':
                $idSubcategoria = 19;
                break;
            case 'terreno/lote':
            case 'lote':
            case 'terreno':
            case 'lotes':
            case 'terrenos':
                $idSubcategoria = 24;
                break;
            case 'vaga de garagem':
                $idSubcategoria = 30;
                break;
            case 'salas/escritórios':
                $idSubcategoria = 20;
                break;
            case 'galpão':
                $idSubcategoria = 21;
                break;
            case 'terreno/lote comercial':
                $idSubcategoria = 22;
                break;
            case 'vaga de garagem':
                $idSubcategoria = 31;
                break;
            case 'veículos':
                $idSubcategoria = 28;
                break;
            case 'diversos':
                $idSubcategoria = 29;
                break;
            default:
                $idSubcategoria = 0;
                break;

        }
        return $idSubcategoria;
    }
}
