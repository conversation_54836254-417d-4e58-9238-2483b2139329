<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class LeiloesSiteController extends Controller
{
    /**
     * List all profiles.
     *
     * @param  int  $id
     * @return redirect()
     */
    public function index(Request $request, $tipo = 'todos')
    {
        $input = $request->all();
        $search = trim(htmlentities($request->keyword));
        if ($search == '') {
            $search = trim(htmlentities($request->search));
        }
        $pagina = isset($input['pagina']) ? (int) $input['pagina'] : null;
        $pagina = $pagina > 0 ? $pagina : 1;
        $pag = 0;
        $qtdLista = 12;

        if($pagina){
            $pag++;
        }

        if($pagina == FALSE || $pagina == 1) {
            $pag = 0;
        }
        else {
            $pag = $pagina - 1;
        }

        $pag = $pag * $qtdLista;
        $strWhere = '';
        $idestado = isset($input['idestado']) && $input['idestado'] != '' ? $input['idestado'] : 0;
        $uf = '';
        if ($idestado !== 0 && strstr($idestado, '|')) {
            $idestado = explode('|', $idestado);
            $uf = $idestado[0];
            $idestado= (int) $idestado[1];
        }

        $idcidade = isset($input['idcidade']) ? $input['idcidade'] : 0;
        $cidade = '';
        $bairro  = isset($input['bairro']) ? $input['bairro'] : '';
        if ($idcidade !== 0 && strstr($idcidade, '|')) {
            $idcidade = explode('|', $idcidade);
            $cidade = $idcidade[0];
            $idcidade= (int) $idcidade[1];
        }
        $input['idcategoria'] = (int) @$input['idcategoria'];
        $valor = isset($input['valor']) ? (int) $input['valor'] : 0;
        $idcategoria = isset($input['idcategoria']) ?  $input['idcategoria'] : 0;
        $idcategoria = $idcategoria > 0 ? $idcategoria : retornaIdCategoria($request->segment(2));
        $input['idsubcategoria'] = (int) @$input['idsubcategoria'];
        $idSubcategoria = isset($input['idsubcategoria']) ?  (int) $input['idsubcategoria'] : 0;
        $idSubcategoria = $idSubcategoria > 0 ? $idSubcategoria : retornaIdSubcategoria($request->segment(3));

        $input['idsubcategoria'] = $input['idsubcategoria'] > 0 ? $input['idsubcategoria'] : $idSubcategoria;
        $input['idcategoria'] = $input['idcategoria'] > 0 ? $input['idcategoria'] : $idcategoria;
        $strWhereTodas = '';
        $strWhereEdicao = ' AND A.tipo in(1,2,3,4, 5, 6) and A.encerrado in(9) and A.suspender = 2 ';
        switch($tipo) {
            case 'judiciais-e-extrajudiciais':
                $strWhere .= ' AND A.tipo in(1,2) and (A.encerrado in(1, 7) ) and A.suspender = 2';
                $strWhereEdicao = ' AND A.tipo in(1,2) and A.encerrado in(9) and A.suspender = 2 ';
            break;
            case 'judiciais-e-extrajudiciais':
                $strWhere .= ' AND A.tipo in(1,2) and (A.encerrado in(1, 7) ) and A.suspender = 2';
                $strWhereEdicao = ' AND A.tipo in(1,2) and A.encerrado in(9) and A.suspender = 2 ';
            break;
            case 'alienacao-fiduciaria':
                $strWhere .= ' AND A.tipo = 6 and (A.encerrado in(1, 7) ) and A.suspender = 2';
                $strWhereEdicao = ' AND A.tipo in(6) and A.encerrado in(9) and A.suspender = 2 ';
            break;
            case 'encerrados':
                $strWhere .= ' AND A.encerrado in(8, 5, 4, 3, 2, 6, 10) and A.suspender = 2';
                $strWhereEdicao = ' and false';
            break;
            case 'judiciais-alienacao-particular':
                $strWhere .= ' AND A.tipo in(1,4) and (A.encerrado in(1, 7) ) and A.suspender = 2';
                $strWhereEdicao = ' AND A.tipo in(1,4) and A.encerrado in(9) and A.suspender = 2 ';
            break;
            case 'venda-direta':
                $strWhere .= ' AND A.tipo in(5) and (A.encerrado in(1, 7) ) and A.suspender = 2';
                $strWhereEdicao = ' AND A.tipo in(5) and A.encerrado in(9) and A.suspender = 2 ';
            break;
            case 'extrajudiciais':
                $strWhere .= ' AND A.tipo in(2) and (A.encerrado in(1, 7) ) and A.suspender = 2';
                $strWhereEdicao = ' AND A.tipo in(2) and A.encerrado in(9) and A.suspender = 2 ';
            break;
            case 'beneficentes':
                $strWhere .= ' AND A.tipo in(3) and (A.encerrado in(1, 7) ) and A.suspender = 2';
                $strWhereEdicao = ' AND A.tipo in(3) and A.encerrado in(9) and A.suspender = 2 ';
            break;
            case 'leiloes-caixa':
                $strWhere .= ' AND A.codigo = 647 AND (A.encerrado in(1, 7, 9) ) ';
                $strWhereEdicao = ' AND A.codigo = 647 AND A.encerrado in(9)  ';
            break;
            case 'todas-as-modalidades':
            default:
                $strWhere .= ' AND (A.encerrado in(1, 7) ) and A.suspender = 2';
		        $strWhereTodas = ' AND (A.encerrado in(1, 7) ) and A.suspender = 2';
		        $strWhereEdicao = ' AND A.tipo in(1,2,3,4, 5, 6) and A.encerrado in(9) and A.suspender = 2 ';
            break;
        }
        $idestado = $idestado > 0 ? $idestado : retornaIdEstado($request->segment(4));
        $idcidade = $idcidade > 0 ? $idcidade : retornaIdCidade($request->segment(5), $idestado);
        if ($idestado > 0) {
            $strWhere .= ' AND B.idestado = ' . $idestado;
            $strWhereEdicao = ' AND B.idestado = ' . $idestado . ' AND A.tipo in(1,2,3,4,5,6) and A.encerrado in(9) and A.suspender = 2 ';
            $uf = $uf != '' ? $uf : $request->segment(5);
            $input['idestado'] = $uf . '|' . $idestado;
        }
        if ($idcidade > 0) {
            $strWhere .= ' AND B.idcidade = ' . $idcidade;
            $strWhereEdicao = ' AND B.idcidade = ' . $idcidade . ' AND A.tipo in(1,2,3,4,5,6) and A.encerrado in(9) and A.suspender = 2 ';
            $cidade = $cidade != '' ? $cidade : $request->segment(5);
            $input['idcidade'] = $cidade . '|' . $idcidade;
        }
        if ($idcategoria > 0 && $idcategoria < 7 && $idcategoria != 3) {
            $strWhere .= ' AND B.categoria = ' . $idcategoria;
            $strWhereEdicao = ' AND B.categoria = ' . $idcategoria . ' AND A.tipo in(1,2,3,4,5,6) and A.encerrado in(9) and A.suspender = 2 ';
            $input['categoria'] = $idcategoria;
        }

        $input['valor'] = $valor;
        switch($valor) {
            case 1:
                $strWhere .= " AND B.min_venda < 100000";
                $strWhereEdicao = ' AND B.min_venda < 100000 AND A.tipo in(1,2,3,4,5,6) and A.encerrado in(9) and A.suspender = 2 ';
            break;
            case 2:
                $strWhere .= " AND B.min_venda >= 100000 AND B.min_venda <= 500000";
                $strWhereEdicao = ' AND B.min_venda  >= 100000 AND A.tipo in(1,2,3,4,5,6) and A.encerrado in(9) and A.suspender = 2 ';
            break;
            case 3:
                $strWhere .= " AND B.min_venda > 500000";
                $strWhereEdicao = ' AND B.min_venda  > 500000 AND A.tipo in(1,2,3,4,5,6) and A.encerrado in(9) and A.suspender = 2 ';
            break;
        }
        $bairro = trim($bairro);
        if ($bairro != '' && $bairro != 'Carregando....') {
            $strWhere .= " AND B.bairro  = :bairro " ;
            $strWhereEdicao = ' AND B.bairro  = :bairro AND A.tipo in(1,2,3,4,5,6) and A.encerrado in(9) and A.suspender = 2 ';
        }

        if ($idSubcategoria > 0) {
            switch($idSubcategoria) {
                case 99:
                    $strWhere .= " AND B.subcategoria  in(18, 25, 19, 24, 30)";
                    $strWhereEdicao = ' AND B.subcategoria  in(18, 25, 19, 24, 30) AND A.tipo in(1,2,3,4, 5, 6) and A.encerrado in(9) and A.suspender = 2 ';
                break;
                case 100:
                    $strWhere .= " AND B.subcategoria  in(20, 21, 22, 31)";
                    $strWhereEdicao = ' AND B.subcategoria  in(20, 21, 22, 31) AND A.tipo in(1,2,3,4, 5, 6) and A.encerrado in(9) and A.suspender = 2 ';
                break;
                default:
                    $strWhere .= " AND B.subcategoria  =   " .$idSubcategoria ;
                    $strWhereEdicao = ' AND B.subcategoria = ' .$idSubcategoria . ' AND A.tipo in(1,2,3,4, 5, 6) and A.encerrado in(9) and A.suspender = 2 ';
                break;
            }
        }

        if ($search !== '') {
		    $strWhere .= " AND (cod_referencia  = :cod_referencia or A.subtitulo like :subtitulo or B.subtitulo like :subtitulo or A.titulo like :titulo or B.titulo like :titulo or B.descricao like :descricao or num_processo = :num_processo  ) " ;
		    $strWhereEdicao = ' AND A.tipo in(1,2,3,4, 5, 6) and A.encerrado in(9) and A.suspender = 2 AND (cod_referencia  = :cod_referencia or A.subtitulo like :subtitulo or B.subtitulo like :subtitulo or A.titulo like :titulo or B.titulo like :titulo or B.descricao like :descricao or num_processo = :num_processo )';
        }
        $orderAberto = "  order by praca asc, A.leilao2_data_final desc,  A.leilao2_hora_final desc";
        $sql = 'select * from (
                                select * from (SELECT A.*, B.idleilao, categoria, B.codigo as codigolote, B.titulo as titulolote,
                                (
                                    SELECT IF(
                                                now() < Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime),
                                                    Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime),
                                                    Cast(concat(B.leilao2_data_final, " ", B.leilao2_hora_final) as datetime)
                                            ) as praca
                                )
                                as praca
                                FROM leiloes A
                                INNER JOIN lotes B ON A.codigo = B.idleilao
                                WHERE (A.encerrado in(1, 7, 6)   and A.suspender = 2)
                                ' . $strWhere . '
                                ' . $strWhereTodas . '
                                AND A.deleted_at is null and B.deleted_at is null
                                ' . $orderAberto . '
                                ) as aberto

                                union all

                                select * from (SELECT A.*,B.idleilao, categoria, B.codigo as codigolote, B.titulo as titulolote,
                                (
                                    SELECT IF(
                                                now() < Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime),
                                                    Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime),
                                                    Cast(concat(B.leilao2_data_final, " ", B.leilao2_hora_final) as datetime)
                                            ) as praca
                                )
                                as praca
                                FROM leiloes A
                                INNER JOIN lotes B ON A.codigo = B.idleilao
                                WHERE A.encerrado = 8 and A.suspender = 2

                                ' . $strWhere . '
                                AND A.deleted_at is null and B.deleted_at is null
                                order by A.leilao2_data_final asc,  A.leilao2_hora_final asc, A.leilao2_data_final asc,  A.leilao2_hora_final asc, praca asc
                                ) as vendido

                                union all

                                select * from (SELECT A.*,B.idleilao, categoria, B.codigo as codigolote, B.titulo as titulolote,
                                (
                                    SELECT IF(
                                                now() < Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime),
                                                    Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime),
                                                    Cast(concat(B.leilao2_data_final, " ", B.leilao2_hora_final) as datetime)
                                            ) as praca
                                )
                                as praca
                                FROM leiloes A
                                INNER JOIN lotes B ON A.codigo = B.idleilao
                                WHERE A.encerrado in (6, 10) and A.suspender = 2

                                ' . $strWhere . '
                                AND A.deleted_at is null and B.deleted_at is null
                                and A.proposta_pos_encerramento = 1
                                order by A.leilao2_data_final asc,  A.leilao2_hora_final asc, A.leilao2_data_final asc,  A.leilao2_hora_final asc, praca asc
                                ) as encerrado

                                union all

                                select * from (SELECT A.*,B.idleilao, categoria, B.codigo as codigolote, B.titulo as titulolote,
                                (
                                    SELECT IF(
                                                now() < Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime),
                                                    Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime),
                                                    Cast(concat(B.leilao2_data_final, " ", B.leilao2_hora_final) as datetime)
                                            ) as praca
                                )
                                as praca
                                FROM leiloes A
                                INNER JOIN lotes B ON A.codigo = B.idleilao
                                WHERE A.encerrado = 3 and A.suspender = 2

                                ' . $strWhere . '
                                AND A.deleted_at is null and B.deleted_at is null
                                order by A.leilao2_data_final asc,  A.leilao2_hora_final asc, A.leilao2_data_final asc,  A.leilao2_hora_final asc, praca asc
                                ) as suspenso

                                union all

                                select * from (SELECT A.*,B.idleilao, categoria, B.codigo as codigolote, B.titulo as titulolote,
                                (
                                    SELECT IF(
                                                now() < Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime),
                                                    Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime),
                                                    Cast(concat(B.leilao2_data_final, " ", B.leilao2_hora_final) as datetime)
                                            ) as praca
                                )
                                as praca
                                FROM leiloes A
                                INNER JOIN lotes B ON A.codigo = B.idleilao
                                WHERE A.encerrado = 5 and A.suspender = 2

                                ' . $strWhere . '
                                AND A.deleted_at is null and B.deleted_at is null
                                order by A.leilao2_data_final asc,  A.leilao2_hora_final asc, A.leilao2_data_final asc,  A.leilao2_hora_final asc, praca asc
                                ) as acordo

                                union all

                                select * from (SELECT A.*,B.idleilao, categoria, B.codigo as codigolote, B.titulo as titulolote,
                                (
                                    SELECT IF(
                                                now() < Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime),
                                                    Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime),
                                                    Cast(concat(B.leilao2_data_final, " ", B.leilao2_hora_final) as datetime)
                                            ) as praca
                                )
                                as praca
                                FROM leiloes A
                                INNER JOIN lotes B ON A.codigo = B.idleilao
                                WHERE A.encerrado = 2 and A.suspender = 2
                                ' . $strWhere . '
                                AND A.deleted_at is null and B.deleted_at is null
                                order by A.leilao2_data_final asc,  A.leilao2_hora_final asc, A.leilao2_data_final asc,  A.leilao2_hora_final asc, praca asc
                                ) as prejudicado


                                union all

                                select * from (SELECT A.*,B.idleilao, categoria, B.codigo as codigolote, B.titulo as titulolote,
                                (
                                    SELECT IF(
                                                now() < Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime),
                                                    Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime),
                                                    Cast(concat(B.leilao2_data_final, " ", B.leilao2_hora_final) as datetime)
                                            ) as praca
                                )
                                as praca
                                FROM leiloes A
                                INNER JOIN lotes B ON A.codigo = B.idleilao
                                WHERE A.encerrado = 4 and A.suspender = 2

                                ' . $strWhere . '
                                AND A.deleted_at is null and B.deleted_at is null
                                order by A.leilao2_data_final asc,  A.leilao2_hora_final asc, A.leilao2_data_final asc,  A.leilao2_hora_final asc, praca asc
                                ) as adjudicado

                                union all

                                select * from (SELECT A.*,B.idleilao, categoria, B.codigo as codigolote, B.titulo as titulolote,
                                (
                                    SELECT IF(
                                                now() < Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime),
                                                    Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime),
                                                    Cast(concat(B.leilao2_data_final, " ", B.leilao2_hora_final) as datetime)
                                            ) as praca
                                )
                                as praca
                                FROM leiloes A
                                INNER JOIN lotes B ON A.codigo = B.idleilao
                                WHERE (A.encerrado in(9)   and A.suspender = 2 )
                                ' . $strWhereEdicao . '
                                AND A.deleted_at is null and B.deleted_at is null
                                ' . $orderAberto . '
                                ) as edicao
            ) as TODOS';

            $selectCount = 'select count(*) as conta from (
                select * from (SELECT A.*,B.idleilao, categoria, B.codigo as codigolote, B.titulo as titulolote,
                (
                    SELECT IF(
                                now() < Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime),
                                    Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime),
                                    Cast(concat(B.leilao2_data_final, " ", B.leilao2_hora_final) as datetime)
                            ) as praca
                )
                as praca
                FROM leiloes A
                INNER JOIN lotes B ON A.codigo = B.idleilao
                WHERE (A.encerrado in(1, 7, 6)   and A.suspender = 2)
                ' . $strWhere . '
                ' . $strWhereTodas . '
                AND A.deleted_at is null and B.deleted_at is null
                ' . $orderAberto . '
                ) as aberto

                union all

                select * from (SELECT A.*,B.idleilao, categoria, B.codigo as codigolote, B.titulo as titulolote,
                (
                    SELECT IF(
                                now() < Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime),
                                    Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime),
                                    Cast(concat(B.leilao2_data_final, " ", B.leilao2_hora_final) as datetime)
                            ) as praca
                )
                as praca
                FROM leiloes A
                INNER JOIN lotes B ON A.codigo = B.idleilao
                WHERE A.encerrado = 8 and A.suspender = 2

                ' . $strWhere . '
                AND A.deleted_at is null and B.deleted_at is null
                order by A.leilao2_data_final asc,  A.leilao2_hora_final asc, A.leilao2_data_final asc,  A.leilao2_hora_final asc, praca asc
                ) as vendido

                union all

                select * from (SELECT A.*,B.idleilao, categoria, B.codigo as codigolote, B.titulo as titulolote,
                (
                    SELECT IF(
                                now() < Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime),
                                    Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime),
                                    Cast(concat(B.leilao2_data_final, " ", B.leilao2_hora_final) as datetime)
                            ) as praca
                )
                as praca
                FROM leiloes A
                INNER JOIN lotes B ON A.codigo = B.idleilao
                WHERE A.encerrado in (6, 10) and A.suspender = 2

                ' . $strWhere . '
                AND A.deleted_at is null and B.deleted_at is null
                and A.proposta_pos_encerramento = 1
                order by A.leilao2_data_final asc,  A.leilao2_hora_final asc, A.leilao2_data_final asc,  A.leilao2_hora_final asc, praca asc
                ) as encerrado

                union all

                select * from (SELECT A.*,B.idleilao, categoria, B.codigo as codigolote, B.titulo as titulolote,
                (
                    SELECT IF(
                                now() < Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime),
                                    Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime),
                                    Cast(concat(B.leilao2_data_final, " ", B.leilao2_hora_final) as datetime)
                            ) as praca
                )
                as praca
                FROM leiloes A
                INNER JOIN lotes B ON A.codigo = B.idleilao
                WHERE A.encerrado = 3 and A.suspender = 2

                ' . $strWhere . '
                AND A.deleted_at is null and B.deleted_at is null
                order by A.leilao2_data_final asc,  A.leilao2_hora_final asc, A.leilao2_data_final asc,  A.leilao2_hora_final asc, praca asc
                ) as suspenso

                union all

                select * from (SELECT A.*,B.idleilao, categoria, B.codigo as codigolote, B.titulo as titulolote,
                (
                    SELECT IF(
                                now() < Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime),
                                    Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime),
                                    Cast(concat(B.leilao2_data_final, " ", B.leilao2_hora_final) as datetime)
                            ) as praca
                )
                as praca
                FROM leiloes A
                INNER JOIN lotes B ON A.codigo = B.idleilao
                WHERE A.encerrado = 5 and A.suspender = 2

                ' . $strWhere . '
                AND A.deleted_at is null and B.deleted_at is null
                order by A.leilao2_data_final asc,  A.leilao2_hora_final asc, A.leilao2_data_final asc,  A.leilao2_hora_final asc, praca asc
                ) as acordo

                union all

                select * from (SELECT A.*,B.idleilao, categoria, B.codigo as codigolote, B.titulo as titulolote,
                (
                    SELECT IF(
                                now() < Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime),
                                    Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime),
                                    Cast(concat(B.leilao2_data_final, " ", B.leilao2_hora_final) as datetime)
                            ) as praca
                )
                as praca
                FROM leiloes A
                INNER JOIN lotes B ON A.codigo = B.idleilao
                WHERE A.encerrado = 2 and A.suspender = 2
                ' . $strWhere . '
                AND A.deleted_at is null and B.deleted_at is null
                order by A.leilao2_data_final asc,  A.leilao2_hora_final asc, A.leilao2_data_final asc,  A.leilao2_hora_final asc, praca asc
                ) as prejudicado


                union all

                select * from (SELECT A.*,B.idleilao, categoria, B.codigo as codigolote, B.titulo as titulolote,
                (
                    SELECT IF(
                                now() < Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime),
                                    Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime),
                                    Cast(concat(B.leilao2_data_final, " ", B.leilao2_hora_final) as datetime)
                            ) as praca
                )
                as praca
                FROM leiloes A
                INNER JOIN lotes B ON A.codigo = B.idleilao
                WHERE A.encerrado = 4 and A.suspender = 2

                ' . $strWhere . '
                AND A.deleted_at is null and B.deleted_at is null
                order by A.leilao2_data_final asc,  A.leilao2_hora_final asc, A.leilao2_data_final asc,  A.leilao2_hora_final asc, praca asc
                ) as adjudicado

                union all

                select * from (SELECT A.*,B.idleilao, categoria, B.codigo as codigolote, B.titulo as titulolote,
                (
                    SELECT IF(
                                now() < Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime),
                                    Cast(concat(B.leilao_data_final, " ", B.leilao_hora_final) as datetime),
                                    Cast(concat(B.leilao2_data_final, " ", B.leilao2_hora_final) as datetime)
                            ) as praca
                )
                as praca
                FROM leiloes A
                INNER JOIN lotes B ON A.codigo = B.idleilao
                WHERE (A.encerrado in(9)   and A.suspender = 2)
                ' . $strWhereEdicao . '
                AND A.deleted_at is null and B.deleted_at is null
                ' . $orderAberto . '
                ) as edicao
) as TODOS';

        $searchSql = html_entity_decode($search);
        $leiloes = DB::select($sql .' limit ' . $pag . ', ' . $qtdLista, ['num_processo' => $searchSql, 'bairro' => addslashes($bairro), 'subcategoria' => $idSubcategoria, 'cod_referencia' => $searchSql, 'subtitulo' => "%$searchSql%", 'titulo' => "%$searchSql%", 'descricao' => "%$searchSql%"] );

        return view('site.leiloes.leiloes', ['bind' => ['num_processo' => $searchSql, 'bairro' => addslashes($bairro), 'subcategoria' => $idSubcategoria, 'cod_referencia' => $searchSql, 'subtitulo' => "%$searchSql%", 'titulo' => "%$searchSql%", 'descricao' => "'%$searchSql%'"] , 'leiloes' => $leiloes, 'search' => $search, 'paginator' => $pagina,'sql' => $selectCount, 'input' => $input, 'request' => $request ]);
    }

}