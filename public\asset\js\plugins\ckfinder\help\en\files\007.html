<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>CKFinder User's Guide</title>
	<link href="../../files/other/help.css" type="text/css" rel="stylesheet" />
	<script type="text/javascript" src="../../files/other/help.js"></script>
	<meta name="robots" content="noindex, nofollow" />
</head>
<body>
	<h1>
		Refresh Button</h1>
	<p>When you work in a shared environment managed by CKFinder, where dozens or even hundreds
		of users are working on the same files at the same time, it may happen that some
		changes are being introduced by others to the files or folders that you are processing
		or viewing at any given moment.</p>
	<p>This is where the <strong>Refresh</strong> option might come to the rescue. This feature
		lets you reload the contents of a folder and see its most current state. Whenever you
		need to see the updated file list, click the <strong>Refresh</strong>
		button from the CKFinder <strong><a href="005.html">Toolbar</a></strong>.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_toolbar_refresh.png" width="251" height="64" alt="Refresh button in the CKFinder toolbar" />&nbsp;</p>
	<p>
		This feature is particularly useful if you are using CKFinder as <em>collaborative
		software</em>, sharing files and folders with friends, family, colleagues, or any other
		group of users. In order to ensure that you always see the most current version of the
		content managed by CKFinder, you might want to occasionally refresh the files list to
		update it.</p>
</body>
</html>
