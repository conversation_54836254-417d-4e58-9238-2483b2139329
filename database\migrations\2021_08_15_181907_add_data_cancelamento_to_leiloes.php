<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Leiloes;

class AddDataCancelamentoToLeiloes extends Migration
{
    public function up()
    {
        Schema::table('leiloes', function (Blueprint $table) {
            $table->dateTime('data_suspensao')->nullable();
        });

        $leiloes = Leiloes::whereIn('encerrado', [2, 3])->get();

        foreach ($leiloes as $leilao) {
            $leilao->data_suspensao = $leilao->updated_at == null ? date('Y-m-d H:i:s') : $leilao->updated_at;
            $leilao->save();
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('leiloes', function (Blueprint $table) {
            $table->dropColumn('data_suspensao');
        });
    }

}
