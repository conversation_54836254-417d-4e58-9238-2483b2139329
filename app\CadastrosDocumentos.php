<?php

namespace App;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use OwenIt\Auditing\Contracts\Auditable;

class CadastrosDocumentos extends Model implements Auditable
{
    use Notifiable;
    use \OwenIt\Auditing\Auditable;

    protected $connection = 'mysql_2';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'idcadastro',
        'arq_pessoal',
        'status_pessoal',
        'arq_residencia',
        'status_residencia',
        'contrato_social',
        'status_contrato_social',
    ];

    protected $table = 'cadastros_documentos';
    protected $primaryKey = 'codigo';
    public $timestamps = false;

    public function __construct() {
    parent::__construct();
    $this->fill($this->fillable);
    $middlewares = (\Route::current());

    if ($middlewares) {
        $middlewares = $middlewares->gatherMiddleware();
        if (in_array('auth', $middlewares) == true) {
            if(session()->has('portaldb')) {
                $this->table = session()->get('portaldb') . '.' . $this->table;
                $this->fill($this->fillable);
            }
        }
    }
}

}