<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>CKFinder User's Guide</title>
	<link href="../../files/other/help.css" type="text/css" rel="stylesheet" />
	<script type="text/javascript" src="../../files/other/help.js"></script>
	<meta name="robots" content="noindex, nofollow" />
</head>
<body>
	<h1>
		CKFinder Interface Overview</h1>
	<p>
		The CKFinder interface is designed to be clean, familiar to end users, and easy to
		learn and use. Most features can be handled with a mouse click as well as by using
		the context menus.</p>
	<p>
		If you are familiar with desktop file browsers built in most operating systems
		available for your PC or laptop, you will quickly see that using an online file
		manager is just as easy and intuitive.</p>
	<p>
		The following is a screenshot of the CKFinder interface:</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_interface.png" alt="CKFinder interface" width="620" height="456" />&nbsp;</p>
	<ol>
		<li><strong><a href="003.html">Folders Pane</a></strong> &ndash; contains the "tree view"
			of the folders that you can navigate. Folders are used to organize and categorize your
			files.</li>
		<li><strong><a href="004.html">Files Pane</a></strong> &ndash; lists the files available
			in the selected folder.</li>
		<li><strong><a href="005.html">Toolbar</a></strong> &ndash; a series of buttons that can
			be clicked in order to quickly execute specific file browser functions.</li>
		<li><strong><a href="010.html">Status Bar</a></strong> &ndash; the section at the bottom
			of the interface that displays some information about the selected file, the total
			number of files in the folder, etc.</li>
		<li><strong><a href="012.html">Context Menu</a></strong> &ndash; a popup menu with file
			browser commands that execute specific tasks for the object that was selected. Options
			available in the context menu change dynamically depending on the object that is
			clicked. </li>
	</ol>
</body>
</html>
