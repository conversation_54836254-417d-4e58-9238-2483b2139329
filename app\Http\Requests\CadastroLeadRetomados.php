<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;
use Auth;

class CadastroLeadRetomados extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'nome_lead' => 'required|min:3|max:80',
            'cpf_lead' => 'required|min:14|max:14',
            'email_lead' => 'required|email:rfc',
            'telefone_lead' => 'required|min:10|max:15',
            'renda_lead' => 'required_if:forma_pagamento,2',
            'nascimento_lead' => 'required_if:forma_pagamento,2',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'nome_lead.required' => 'Informe o seu nome',
            'cpf_lead.required' => 'Informe seu CPF',
            'cpf_lead.min' => 'O CPF deve conter, no mínimo, :min caracteres',
            'cpf_lead.max' => 'O CPF deve conter, no máximo, :max caracteres',
            'nome_lead.max' => 'O nome deve conter, no máximo :max caracteres',
            'nome_lead.min' => 'O nome deve conter, no mínimo :min caracteres',
            'telefone_lead.required' => 'O telefone deve ser informado',
            'email_lead.required' => 'O email deve ser informado',
            'renda_lead.required_if' => 'A renda bruta deve ser informada',
            'nascimento_lead.required_if' => 'Data de nascimento deve ser informada',
        ];
    }
}
