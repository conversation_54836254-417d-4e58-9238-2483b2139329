<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Leiloes;
use App\Portal;
use App\Http\Requests\CadastroLeiloesRequest;
use App\Http\Requests\ReplicarLeilaoRequest;
use App\Services\RelatorioResultadoLeilaoService;
use Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class LeiloesController extends Controller
{
    private $table = 'leiloes';
    private $modelInstance;


    private function setPortal($request) {
        if ($request->portal ) {
            $portal = Portal::where('database', $request->portal)->first();

            if ($portal) {
                $request->session()->put('portaldb', $portal->database);
                $request->session()->put('path_logo', $portal->path_logo);
                return true;
            }
        }
        return false;
    }
    /**
     * Show the profile for the given user.
     *
     * @param  int  $id
     * @return View
     */
    public function show($id)
    {
        return view('admin.leiloes.index', ['user' => User::findOrFail($id)]);
    }
    /**
     * List all profiles.
     *
     * @param  int  $id
     * @return redirect()
     */
    public function index(Request $request)
    {
        $this->setPortal($request);

        $vet = Leiloes::whereIn('encerrado', [1, 7, 9, 11])->where('parceiro', '!=', true)->get();

        return view('admin.leiloes.index', ['cads' => $vet]);
    }

    public function listaEncerrados()
    {
        $vet = Leiloes::whereNotIn('encerrado', [1, 7, 9, 11])->where('parceiro', '!=', true)->get();
        return view('admin.leiloes.index-encerrados', ['cads' => $vet]);
    }

    /**
     * List all profiles.
     *
     * @param  Request  $request
     * @return redirect()
     */

    public function search(Request $request)
    {
        $input = $request->all();
        $cadastros = new Cadastros();
        $nome = trim($input['nome']);
        $cpfCnpj = trim($input['cpf_cnpj']);
        $status = (int) $input['status'];
        $dataInicial = trim($input['data_inicial']);
        $dataFinal = trim($input['data_final']);

        if ($nome != '') {
            $cadastros = $cadastros->where('nome', 'like', '%' . $nome . '%')
                                ->orWhere('razao_social', 'like ', '%' . $nome . '%');
        }

        if ($cpfCnpj != '') {
            $cadastros = $cadastros->where('cpf', 'like', '%' . $cpfCnpj . '%')
                                ->orWhere('cnpj', 'like ', '%' . $cpfCnpj . '%');
        }

        if ($status != 10) {
            $cadastros = $cadastros->where('status', '=',  $status );
        }

        if ($dataInicial != '' && $dataFinal != '') {
            $dataInicial = explode('/', $dataInicial);
            $dataFinal = explode('/', $dataFinal);
            $dataInicial = $dataInicial[2] . '-' . $dataInicial[1] . '-' . $dataInicial[0];
            $dataFinal = $dataFinal[2] . '-' . $dataFinal[1] . '-' . $dataFinal[0];
            $cadastros = $cadastros->whereBetween('data_cadastro', [$dataInicial, $dataFinal]);
        }
        $cadastros = $cadastros->get();

        return view('admin.cads.index', ['cads' => $cadastros, 'input' => $input]);
    }

    /**
     * Update the profile for the given user.
     *
     * @param  int  $id
     * @return redirect()
     */
    public function save(CadastroLeiloesRequest $request)
    {
        $input = $request->all();

        $cadastro = new Leiloes();
        $input['codigo'] = (int) $input['codigo'];
        $cadastro->data_cadastro = date('Y-m-d') ;
        if ($input['codigo'] > 0) {
            $cadastro = Leiloes::findOrFail($input['codigo']);
            unset($cadastro->data_cadastro);
        }
        $cadastro->tipo     = $input['tipo'];
        $input['modalidade'] = trim($input['modalidade']);
        $cadastro->modalidade   = $input['modalidade'] == '' ? 1 : $input['modalidade'];
        $cadastro->cod_referencia   = $input['cod_referencia'];
        $cadastro->modelo       = 1;//$input['modelo'];
        $cadastro->idleiloeiro  = 3;//(int) $input['idleiloeiro'];
        $cadastro->habilitacao   = 1;//(int) $input['habilitacao'];
        $cadastro->titulo  = $input['titulo'];
        $cadastro->subtitulo = $input['subtitulo'];
        $cadastro->suspender = (int) $input['suspender'];

        $cadastro->numero            = '';
        $cadastro->publicacao    = @$input['publicacao'];
        $cadastro->idcomitente      = 0;//$input['idcomitente'];
        $cadastro->leilao_data_tipo      = (int) $input['leilao_data_tipo'];
        $input['leilao_data_inicial'] = trim ($input['leilao_data_inicial']);
        $input['leilao_data_final'] = trim ($input['leilao_data_final']);
        $cadastro->encerrado    = (int) $input['encerrado'];

        if ($cadastro->encerrado != '9') {

            if ($input['leilao_data_inicial'] == '') {
                return redirect()->back()->with('error', 'Data inicial não foi informada.');
            }

            if ($input['leilao_data_final'] == '') {
                return redirect()->back()->with('error', 'Data final não foi informada.');
            }

            if (!checkValidDate($input['leilao_data_inicial'])) {
                return redirect()->back()->with('error', 'Data inicial do primeiro período não é válida [' . $input['leilao_data_inicial'] . ']. Verifique e tente novamente.');
            }

            if (!checkValidDate($input['leilao_data_final'])) {
                return redirect()->back()->with('error', 'Data final do primeiro período não é válida [' . $input['leilao_data_final'] . ']. Verifique e tente novamente.');
            }

            $timeStampDatainicial = \Carbon\Carbon::createFromFormat('d/m/Y', $input['leilao_data_inicial'])->timestamp;

            $timeStampDatafinal = \Carbon\Carbon::createFromFormat('d/m/Y', $input['leilao_data_final'])->timestamp;
            $timeStampDatainicial2 = \Carbon\Carbon::createFromFormat('d/m/Y', $input['leilao2_data_inicial'] ?? $input['leilao_data_inicial'] )->timestamp;
            $timeStampDatafinal2 = \Carbon\Carbon::createFromFormat('d/m/Y', $input['leilao2_data_final'] ?? $input['leilao_data_final'])->timestamp;

            $cadastro->leilao_data_inicial   = date('Y-m-d', $timeStampDatainicial);
            $cadastro->leilao_hora_inicial   = $input['leilao_hora_inicial'];

            $cadastro->leilao_data_final    = date('Y-m-d', $timeStampDatafinal);
            $cadastro->leilao_hora_final    = $input['leilao_hora_final'];
            $cadastro->leilao2_data_tipo   = $input['leilao_data_tipo'];
            $cadastro->leilao2_data_inicial   = date('Y-m-d', $timeStampDatainicial2);
            $cadastro->leilao2_hora_inicial   = $input['leilao2_hora_inicial'] ?? $input['leilao_hora_inicial'];
            $cadastro->leilao2_data_final     = date('Y-m-d', $timeStampDatafinal2);
            $cadastro->leilao2_hora_final     = $input['leilao2_hora_final'] ?? $input['leilao_hora_final'];
        }

        $cadastro->responsavel           = '';//$input['responsavel'];
        $cadastro->endereco            = '';

        $cadastro->condicao            = trim($input['condicao']);
        $cadastro->desconto            = trim($input['desconto']);

        $cadastro->cidade           = '';
        $cadastro->visitacao        = '';
        $cadastro->restrito         = 2;
        $cadastro->usar_cronometro       = 1;
        $cadastro->proposta_pos_encerramento       = (int) $input['proposta_pos_encerramento'];

        $time = time();
        if ($request->jornal) {
            $edital = $request->jornal->getClientOriginalName();
            $ext = $request->jornal->getClientOriginalExtension();

            $cadastro->jornal       = md5($request->jornal->getClientOriginalName() .$time . $cadastro->titulo . $input['codigo']) . '.' . $ext;
            $request->jornal->storeAs('documentos/leilao', $cadastro->jornal );
        }
        if ($request->edital) {
            $edital = $request->edital->getClientOriginalName();
            $ext = $request->edital->getClientOriginalExtension();
            $edital = str_replace([' ', '/', '\\', '(', ')', '=', '+', '_'], '-', $edital);
            $edital = str_replace('.'.$ext, '', $edital);
            $edital = $edital . '-' . md5($edital.$time.$input['codigo']) . '.' . $ext;

            $cadastro->edital    = $edital;
            $request->edital->storeAs('documentos/leilao', $cadastro->edital);
        }
        if ($request->logo) {
            $cadastro->logo        = $request->logo->getClientOriginalName();
            $request->logo->storeAs('storage/imagens', $cadastro->logo);
        }
        if ($request->destaque) {
            $imagem  = str_replace(" ", "-", $request->destaque->getClientOriginalName());
            $ext = explode('.', $imagem);
            $ext = $ext[count($ext) - 1];
            $imagem =  md5($time . $imagem . $input['titulo']) . '.' . $ext;
            $cadastro->destaque     = $imagem;
            $request->destaque->storeAs('storage/imagens', $cadastro->destaque);

            $request->destaque->storeAs('public/imagens/thumb', $cadastro->destaque, 'local');
            $imgThumb = storage_path('app/public') . '/imagens/thumb/'.$cadastro->destaque;
            redimencionaImagem($imgThumb, 250, 166, 'storage/imagens/thumb/' . $cadastro->destaque);
        }
        $cadastro->imagem_360 = '';
        if ($request->imagem_360) {
            $cadastro->imagem_360   = $request->imagem_360->getClientOriginalName();
            $request->imagem_360->storeAs('public/imagens', $cadastro->imagem_360);
        }
        $cadastro->youtube      = $input['youtube'];
        $cadastro->condicoes    = $input['condicoes'];
        $cadastro->regras       = $input['regras'];
        $cadastro->desconto_p   = (int) $input['desconto_p'];

        $cadastro->delay_encerramento_lotes =  (int) $input['delay_encerramento_lotes'];

        //status futuro
        $timeStampDatainicial = strtotime($cadastro->leilao_data_inicial . ' ' . $cadastro->leilao_hora_inicial);

        if ($cadastro->encerrado === 2 || $cadastro->encerrado === 3) {
            $cadastro->data_suspensao = date('Y-m-d H:i:s');
        }

        $cadastro->save();
        $loteAbertura = null;
        $loteFechamento = null;
        if ($cadastro->leilao_data_inicial) {
            $loteAbertura = explode('-', $cadastro->leilao_data_inicial);
            $loteAbertura = explode('-', $cadastro->leilao_data_inicial);
            $loteAbertura = $loteAbertura[2] . '/' . $loteAbertura[1] . '/' . $loteAbertura[0] . ' ' . substr($cadastro->leilao_hora_inicial, 0, 5);

            $loteFechamento = explode('-', $cadastro->leilao2_data_final);
            $loteFechamento = explode('-', $cadastro->leilao2_data_final);
            $loteFechamento = $loteFechamento[2] . '/' . $loteFechamento[1] . '/' . $loteFechamento[0] . ' ' . substr($cadastro->leilao2_hora_final, 0, 5);
        }

        $minEncerramentoProximosLotes = $cadastro->delay_encerramento_lotes * 60;

        //atualiza os lotes, exceto os que estão abertos ou encerrados

        // if (isset($input['reset_leilao'])){
        //     $lotes = \App\Lotes::where('idleilao','=', $cadastro->codigo)->orderBy('codigo', 'asc')->get();
        // }
        // else
        // {
        //     $lotes = \App\Lotes::where('idleilao','=', $cadastro->codigo)->whereNotIn('encerrado', [1, 6, 2, 3, 8, 9, 10])->orderBy('codigo', 'asc')->get();
        // }

        $lotes = \App\Lotes::where('idleilao','=', $cadastro->codigo)->orderBy('numero_lote', 'asc')->orderBy('codigo', 'asc')->get();

        $primeiroLote = true;

        foreach($lotes as $lote) {
            $strTimeDataFinal1 = strtotime($cadastro->leilao_data_final . ' ' . $cadastro->leilao_hora_final);
            $strTimeDataFinal2 = strtotime($cadastro->leilao2_data_final . ' ' . $cadastro->leilao2_hora_final);

            $lote2DataFinal = $cadastro->leilao2_data_final;
            $lote2HoraFinal = $cadastro->leilao2_hora_final;
            $loteDataFinal = $cadastro->leilao_data_final;
            $loteHoraFinal = $cadastro->leilao_hora_final;

            if ($primeiroLote == false) {
                $strTimeDataFinal1 = $strTimeDataFinal1 + $minEncerramentoProximosLotes;
                $strTimeDataFinal2 = $strTimeDataFinal2 + $minEncerramentoProximosLotes;
                $loteDataFinal = date('Y-m-d', $strTimeDataFinal1);
                $loteHoraFinal = date('H:i:s', $strTimeDataFinal1);

                $lote2DataFinal = date('Y-m-d', $strTimeDataFinal2);
                $lote2HoraFinal = date('H:i:s', $strTimeDataFinal2);
                $loteFechamento = date('d/m/Y H:i', $strTimeDataFinal2);

                $minEncerramentoProximosLotes = $minEncerramentoProximosLotes + ($cadastro->delay_encerramento_lotes * 60);
            }
            $primeiroLote = false;

            $lote->encerrado = $cadastro->encerrado;
            $lote->abertura = $loteAbertura;
            $lote->fechamento = $loteFechamento;
            $lote->leilao2_data_final = $lote2DataFinal;
            $lote->leilao2_hora_final = $lote2HoraFinal;
            $lote->leilao_data_final = $loteDataFinal;
            $lote->leilao_hora_final = $loteHoraFinal;

            $lote->leilao2_data_inicial = $cadastro->leilao2_data_inicial;
            $lote->leilao2_hora_inicial = $cadastro->leilao2_hora_inicial;
            $lote->leilao_data_inicial = $cadastro->leilao_data_inicial;
            $lote->leilao_hora_inicial = $cadastro->leilao_hora_inicial;
            $lote->save();
        }

        \App\Lotes::where('idleilao','=', $cadastro->codigo)
                   ->update([
                       'encerrado' => $cadastro->encerrado,
                       'abertura' => $loteAbertura,
                       'fechamento' => $loteFechamento]);

        if(strstr($request->getUri(), '/encerrados')) {
            return redirect('/leiloes/arquivados/encerrados')->with('success', 'Operação concluída.');
        }

        return redirect('/leiloes/cadastro')->with('success', 'Operação concluída.');
    }

    /**
     * Delete the profile for the given user.
     *
     * @param  int  $id
     * @return redirect()
     */
    public function delete(Request $request, $id)
    {
        $id = (int) $id;
        $user = Auth::guard('admin')->user();
        $cadastro = Leiloes::findOrFail($id);
        $titulo = $cadastro->titulo;
        $cadastro->delete();

        \App\Lotes::where('idleilao', $id)->delete();
        Log::channel('leiloes')->info('Leilão ' . $id . ' [' . $titulo .'] excluído por ' . $user->nome . ' [' . $user->email . ']');
        $request->session()->flash('success', 'Leilao Excluído');
        return response()->json(['status' => 'ok']);
    }

    public function deleteEncerrado($id)
    {
        $id = (int) $id;
        $cadastro = Leiloes::find($id);
        $cadastro->delete();
        return redirect('/leiloes/arquivados/encerrados');
    }

    /**
     * Show the profile for the given user.
     *
     * @param  int  $id
     * @return View
     */
    public function get($id)
    {
        $id = (int) $id;
        $cadastros = Leiloes::whereIn('encerrado', [1, 7, 9, 11])->get();
        $leilao = Leiloes::where('codigo', $id)->where('parceiro', false)->firstOrFail()->toArray();
        return view('admin.leiloes.index', ['cads' => $cadastros, 'vet' => $leilao]);
    }

    public function getEncerrado($id)
    {
        $id = (int) $id;
        $cadastros = Leiloes::whereNotIn('encerrado', [1, 7, 9, 11])->where('parceiro', false)->get();
        return view('admin.leiloes.index-encerrados', ['cads' => $cadastros, 'vet' => Leiloes::findOrFail($id)]);
    }

    public function replicar(ReplicarLeilaoRequest $request, $id) {
        try {
            $urlLeilao = '';
            DB::transaction(function () use($request, $id, &$urlLeilao) {
                $input = $request->all();
                $leilaoOld = Leiloes::find($id);
                $leilaoNew = $leilaoOld->replicate();

                $input['leilao_data_inicial_replica'] = trim ($input['leilao_data_inicial_replica']);
                $input['leilao_data_final_replica'] = trim ($input['leilao_data_final_replica']);
                $input['modalidade_replica'] = trim($input['modalidade_replica']);

                $timeStampDatainicial = \Carbon\Carbon::createFromFormat('d/m/Y', $input['leilao_data_inicial_replica'])->timestamp;
                $timeStampDatafinal = \Carbon\Carbon::createFromFormat('d/m/Y', $input['leilao_data_final_replica'])->timestamp;
                $timeStampDatainicial2 = \Carbon\Carbon::createFromFormat('d/m/Y', $input['leilao2_data_inicial_replica'] ?? $input['leilao_data_inicial_replica'] )->timestamp;
                $timeStampDatafinal2 = \Carbon\Carbon::createFromFormat('d/m/Y', $input['leilao2_data_final_replica'] ?? $input['leilao_data_final_replica'])->timestamp;

                $leilaoNew->leilao_data_inicial   = date('Y-m-d', $timeStampDatainicial);
                $leilaoNew->leilao_hora_inicial   = $input['leilao_hora_inicial_replica'];

                $leilaoNew->leilao_data_final    = date('Y-m-d', $timeStampDatafinal);
                $leilaoNew->leilao_hora_final    = $input['leilao_hora_final_replica'];
                $leilaoNew->leilao2_data_tipo   = $input['leilao_data_tipo_replica'];
                $leilaoNew->leilao2_data_inicial   = date('Y-m-d', $timeStampDatainicial2);
                $leilaoNew->leilao2_hora_inicial   = $input['leilao2_hora_inicial_replica'] ?? $input['leilao_hora_inicial_replica'];
                $leilaoNew->leilao2_data_final     = date('Y-m-d', $timeStampDatafinal2);
                $leilaoNew->leilao2_hora_final     = $input['leilao2_hora_final_replica'] ?? $input['leilao_hora_final_replica'];
                $leilaoNew->modalidade   = $input['modalidade_replica'] == '' ? 1 : $input['modalidade_replica'];
                $leilaoNew->leilao_data_tipo      = (int) $input['leilao_data_tipo_replica'];
                $leilaoNew->tipo     = (int) $input['tipo_replica'];
                $leilaoNew->cod_referencia   = $input['cod_referencia'];
                $leilaoNew->encerrado = (int) $input['encerrado'];
                $leilaoNew->save();


                $loteAbertura = explode('-', $leilaoNew->leilao_data_inicial);
                $loteAbertura = explode('-', $leilaoNew->leilao_data_inicial);
                $loteAbertura = $loteAbertura[2] . '/' . $loteAbertura[1] . '/' . $loteAbertura[0] . ' ' . substr($leilaoNew->leilao_hora_inicial, 0, 5);

                $loteFechamento = explode('-', $leilaoNew->leilao2_data_final);
                $loteFechamento = explode('-', $leilaoNew->leilao2_data_final);
                $loteFechamento = $loteFechamento[2] . '/' . $loteFechamento[1] . '/' . $loteFechamento[0] . ' ' . substr($leilaoNew->leilao2_hora_final, 0, 5);

                $minEncerramentoProximosLotes = $leilaoNew->delay_encerramento_lotes * 60;

                $lotes = $leilaoOld->lots()->get();

                $primeiroLote = true;
                $urlLeilao =  '/leiloes/cadastro/' . $leilaoNew->codigo;
                foreach($lotes as $lote) {
                    $anexos = $lote->anexosLote()->get();
                    $lote = $lote->replicate();
                    $lote->idleilao = $leilaoNew->codigo;
                    $strTimeDataFinal1 = strtotime($leilaoNew->leilao_data_final . ' ' . $leilaoNew->leilao_hora_final);
                    $strTimeDataFinal2 = strtotime($leilaoNew->leilao2_data_final . ' ' . $leilaoNew->leilao2_hora_final);

                    $lote2DataFinal = $leilaoNew->leilao2_data_final;
                    $lote2HoraFinal = $leilaoNew->leilao2_hora_final;
                    $loteDataFinal = $leilaoNew->leilao_data_final;
                    $loteHoraFinal = $leilaoNew->leilao_hora_final;

                    if ($primeiroLote == false) {
                        $strTimeDataFinal1 = $strTimeDataFinal1 + $minEncerramentoProximosLotes;
                        $strTimeDataFinal2 = $strTimeDataFinal2 + $minEncerramentoProximosLotes;
                        $loteDataFinal = date('Y-m-d', $strTimeDataFinal1);
                        $loteHoraFinal = date('H:i:s', $strTimeDataFinal1);

                        $lote2DataFinal = date('Y-m-d', $strTimeDataFinal2);
                        $lote2HoraFinal = date('H:i:s', $strTimeDataFinal2);
                        $loteFechamento = date('d/m/Y H:i', $strTimeDataFinal2);

                        $minEncerramentoProximosLotes = $minEncerramentoProximosLotes + ($leilaoNew->delay_encerramento_lotes * 60);
                    }
                    $primeiroLote = false;

                    $lote->encerrado = $leilaoNew->encerrado;
                    $lote->abertura = $loteAbertura;
                    $lote->fechamento = $loteFechamento;
                    $lote->leilao2_data_final = $lote2DataFinal;
                    $lote->leilao2_hora_final = $lote2HoraFinal;
                    $lote->leilao_data_final = $loteDataFinal;
                    $lote->leilao_hora_final = $loteHoraFinal;

                    $lote->leilao2_data_inicial = $leilaoNew->leilao2_data_inicial;
                    $lote->leilao2_hora_inicial = $leilaoNew->leilao2_hora_inicial;
                    $lote->leilao_data_inicial = $leilaoNew->leilao_data_inicial;
                    $lote->leilao_hora_inicial = $leilaoNew->leilao_hora_inicial;

                    if ( (int)$input['tipo_replica'] == 4) {
                        $lote->lance_data_1 = $lote->lance_data_2;
                    }

                    $lote->save();
                    foreach($anexos as $anexo) {
                        $arquivo = new \App\LotesAnexos;
                        $arquivo->idlote = $lote->codigo;
                        $arquivo->arquivo = $anexo->arquivo;
                        $arquivo->nome = $anexo->nome;
                        $arquivo->save();
                    }
                }
            });
            if($request->ajax()) {
                return response()->json(['msg' => 'Operação concluída', 'url' => $urlLeilao], 200);
            }
            return response()->json(['errors' => ['message' => ['Operação não pode ser completada. Tente novamente']]], 401);
        } catch (\Exception $e) {
            return response()->json(['errors' => ['message' => [$e->getMessage()]]], 500);
        }
    }


    public function exportaResultadoLeilao($id) {
        $lote = Leiloes::findOrFail($id);
        $relatorio = new RelatorioResultadoLeilaoService();
        $fileName = $relatorio->exportaTransacoesLeilao($lote);
        return response()->download('../tmp/' . $fileName);
    }
}
