<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>CKFinder User's Guide</title>
	<link href="../../files/other/help.css" type="text/css" rel="stylesheet" />
	<script type="text/javascript" src="../../files/other/help.js"></script>
	<meta name="robots" content="noindex, nofollow" />
</head>
<body>
	<h1>
		Basket</h1>
	<p>
		The <strong>Basket</strong> folder that is available in the
		<strong><a href="003.html">Folders Pane</a></strong> opens the
		<strong>Basket Pane</strong>.</p>
	<p>
		The <strong>Basket</strong> is a virtual and temporary placeholder that can
		be used to perform batch operations on files in CKFinder. It is a
		<em>virtual</em> container, since the files that are placed in the
		<strong>Basket</strong> are not physically moved from their parent folders.
		</p>
	<p>The figure below presents the file browser <strong>Basket Pane</strong>
		that is expanded when you click the <strong>Basket</strong> folder in the
		<strong>Folders Pane</strong>.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_basket_empty.png" width="548" height="235" alt="CKFinder Basket folder" />&nbsp;</p>
	<p>
		<span class="info">Note:</span> The <strong>Basket</strong> folder is private
		in the sense that it is not being shared with other users of the file system
		and is tied to your browser session.</p>
	<h2>
		Adding Files to Basket</h2>
	<p>
		At the beginning of each CKFinder session the <strong>Basket</strong> is empty.
		A message urging you to drag some files and drop them into the <strong>Basket</strong>
		will be displayed. In order to do this, go to the folder that contains the files
		that you are going to use, drag it onto the <strong>Basket</strong> folder in
		the <strong>Folders Pane</strong>, and drop.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_basket_drag.png" width="482" height="303" alt="Dragging a file to the Basket in CKFinder" />&nbsp;</p>
	<p>The file should now appear in the <strong>Basket</strong> folder.</p>
	<h2>
		File Context Menu</h2>
	<p>
		When a file is placed in the <strong>Basket</strong>, its context menu will change
		to only include the operations that are available in this special folder.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_basket_file_menu.png" width="137" height="130" alt="File context menu opened in the Basket" />&nbsp;</p>
	<h2>
		Removing Files from Basket</h2>
	<p>
		There are two methods of removing a file from the <strong>Basket</strong>. Firstly,
		you can remove an individual file by choosing the <strong>Remove from Basket</strong>
		option from the file context menu. When you select the context menu removal option,
		a confirmation message will appear.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_basket_remove.png" width="342" height="149" alt="Removing a file from the Basket in CKFinder" />&nbsp;</p>
	<p>You can also remove all files at once by clicking the <strong>Clear Basket</strong> toolbar
		button. When you do this, you will be prompted to confirm whether you intend to remove
		all <strong>Basket</strong> contents.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_basket_clear.png" width="304" height="149" alt="Removing all files from the Basket in CKFinder" />&nbsp;</p>
	<p>
		<span class="info">Note:</span> Removing a file from the <strong>Basket</strong> does
		not delete it from the file system. It will still be available in its parent folder.</p>
	<h2>
		Showing File Parent Folder</h2>
	<p>
		As mentioned above, the <strong>Basket</strong> is a virtual folder that lists files that
		are physically located in other file system folders. If you want to check the source folder
		of a file, choose the <strong>Open Parent Folder</strong> option from the file context
		menu. CKFinder will show the folder that the file is located in.</p>
	<h2>
		Copying Files from Basket</h2>
	<p>
		Once you conveniently place some files in the <strong>Basket</strong>, you will now be
		able to copy them to a different (physical) folder. In order to achieve this, select the
		target folder in the <strong><a href="003.html">Folders Pane</a></strong> and choose the
		<strong>Copy Files from Basket</strong> option from its context menu.</p>
	<p>
		A confirmation message will appear, listing the files that were copied to the target
		folder.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_folder_copied_from_basket.png" width="304" height="206" alt="Copying files from Basket in CKFinder" />&nbsp;</p>
	<p>
		The files will be duplicated and their copies will be placed in the target folder. The
		source folder will remain untouched.</p>
	<h2>Moving Files from Basket</h2>
	<p>
		The <strong>Basket</strong> is also useful if you want to move some files between folders.
		Once you place some files in the <strong>Basket</strong>, select a target folder in the
		<strong>Folders Pane</strong>, and choose the <strong>Move Files from Basket</strong> option
		from its context menu.</p>
	<p>
		A confirmation message will appear, listing the files that were moved to the target folder.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_folder_moved_from_basket.png" width="304" height="206" alt="Moving files from Basket in CKFinder" />&nbsp;</p>
	<p>
		The files will be removed from the source folder and pasted into the target folder.</p>
</body>
</html>
