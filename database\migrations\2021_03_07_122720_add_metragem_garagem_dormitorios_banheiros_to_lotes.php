<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMetragemGaragemDormitoriosBanheirosToLotes extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('lotes', function (Blueprint $table) {
            $table->integer('metragem')->default(0);
            $table->integer('dormitorios')->default(0);
            $table->integer('garagem')->default(0);
            $table->integer('banheiros')->default(0);
	        $table->integer('situacao')->default(0);
            $table->longText('desc_condicao_pagamento')->default(null);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('lotes', function (Blueprint $table) {
            $table->dropColumn('metragem');
            $table->dropColumn('dormitorios');
            $table->dropColumn('garagem');
            $table->dropColumn('banheiros');
	        $table->dropColumn('situacao');
            $table->dropColumn('desc_condicao_pagamento');
        });
    }
}
