<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<title>iframe</title>

		<style>
			html,body{
				margin: 0;
				height: 100%;
				font: 13px/1.555 "Trebuchet MS", sans-serif;
			}
			a{
			    color: #888;
			    font-weight: bold;
			    text-decoration: none;
			    border-bottom: 1px solid #888;
			}
			.main-box {
				color:#252525;
				padding: 3px 5px;
				text-align: justify;
			}
			.main-box p{margin: 0 0 14px;}
			.main-box .cerr{
			    color: #f00000;
			    border-bottom-color: #f00000;
			}
		</style>
	</head>
	<body>
		<div id="content" class="main-box"></div>
		<iframe src="" frameborder="0" id="spelltext" name="spelltext" style="display:none; width: 100%" ></iframe>
		<iframe src="" frameborder="0" id="loadsuggestfirst" name="loadsuggestfirst" style="display:none; width: 100%" ></iframe>
		<iframe src="" frameborder="0" id="loadspellsuggestall" name="loadspellsuggestall" style="display:none; width: 100%" ></iframe>
		<iframe src="" frameborder="0" id="loadOptionsForm" name="loadOptionsForm" style="display:none; width: 100%" ></iframe>
		<script>
		(function(window) {
			// Constructor Manager PostMessage

			var ManagerPostMessage = function() {
				var _init = function(handler) {
					if (document.addEventListener) {
						window.addEventListener('message', handler, false);
					} else {
						window.attachEvent("onmessage", handler);
					};
				};
				var _sendCmd = function(o) {
					var str,
						type = Object.prototype.toString,
						fn = o.fn || null,
						id = o.id || '',
						target = o.target || window,
						message = o.message || { 'id': id };

					if (type.call(o.message) == "[object Object]") {
						(o.message['id']) ? o.message['id'] : o.message['id'] = id;
						message = o.message;
					};

					str = JSON.stringify(message, fn);
					target.postMessage(str, '*');
				};

				return {
					init: _init,
					send: _sendCmd
				};
			};

			var manageMessageTmp = new ManagerPostMessage;


				var appString = (function(){
					var spell = parent.CKEDITOR.config.wsc.DefaultParams.scriptPath;
					var serverUrl = parent.CKEDITOR.config.wsc.DefaultParams.serviceHost;
					return serverUrl + spell;
				})();

				function loadScript(src, callback) {
				    var scriptTag = document.createElement("script");
				   		scriptTag.type = "text/javascript";
				   	callback ? callback : callback = function() {};
				    if(scriptTag.readyState) {
				        //IE
				        scriptTag.onreadystatechange = function() {
				            if (scriptTag.readyState == "loaded" ||
				            scriptTag.readyState == "complete") {
				                scriptTag.onreadystatechange = null;
				                setTimeout(function(){scriptTag.parentNode.removeChild(scriptTag)},1)
				                callback();
				            }
				        };
				    }else{
				        //Others
				        scriptTag.onload = function() {
				           setTimeout(function(){scriptTag.parentNode.removeChild(scriptTag)},1);
				           callback();
				        };
				    };
				    scriptTag.src = src;
				    document.getElementsByTagName("head")[0].appendChild(scriptTag);
				};


				window.onload = function(){
					 loadScript(appString, function(){
						manageMessageTmp.send({
							'id': 'iframeOnload',
							'target': window.parent
						});
					});
				}
			})(this);
		</script>
	</body>
</html>
