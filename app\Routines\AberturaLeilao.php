<?php

namespace App\Routines;

use App\Leiloes;
use App\Lotes;
use Illuminate\Support\Facades\Log;

class AberturaLeilao
{
    public static function abertura($lote, $voltaFuturo = false)
    {
        $lote = (int) $lote;

        Log::info('Abertura de Lotes: CRON - Iniciando');
        $lotes = Lotes::where('encerrado', 7)->where('leilao_data_inicial', '<=', date('Y-m-d'));
        if ($lote > 0) {
            $lotes = $lotes->where('codigo', $lote);
        }
        $lotes = $lotes->get();
        $qtdLotes = 0;
        foreach ($lotes as $lote) {
            if ($lote->leilao == null) {
                Log::info('Este lote não possui leilao atrelado. ' . $lote->codigo);
                Log::info('Excluindo lote por não possuir leilão atrelado. ' . $lote->codigo);
                $lote->delete();
                continue;
            }
            Log::info('Tentando abrir lote ' . $lote->codigo);
            if (self::decideAbertura($lote)) {
                $qtdLotes++;
            }
        }
        Log::info('Abertura de leilões: Fim. ' . $qtdLotes . ' lotes afetados');
    }

    private static function decideAbertura($lote)
    {
        // Define o fuso horário com base no estado do leilão
        if ($lote->num_processo) {
            $timezone = getFuso($lote->num_processo);
        } else {
            $timezone = self::getTimezoneByUF($lote->estado->uf);
        }
        date_default_timezone_set($timezone);

        $timeNow = time();
        $dataInicial = strtotime($lote->leilao2_data_inicial . ' ' . $lote->leilao2_hora_inicial);
        $primeiraPraca = strtotime($lote->leilao_data_final . ' ' . $lote->leilao_hora_final);
        $primeiraPraca  = $timeNow - $primeiraPraca  <=  3 ? true : false;

        $hasFile = \Storage::disk('local')->has('lotefuturo' . $lote->codigo);
        if ($primeiraPraca && !$hasFile) {
            $dataInicial = strtotime($lote->leilao_data_inicial . ' ' . $lote->leilao_hora_inicial);
        }

        $verificaData = date('Y', $dataInicial);
        if ($verificaData == '-1969' || $verificaData == '-0001') {
            Log::info('Abertura de leilões: Lote ainda não será aberto ' . $lote->codigo . '=> Ano inválido ' . date('d/m/Y H:i:s', $dataInicial));
            return false;
        }

        if ($timeNow > $dataInicial && !$hasFile) {
            $modelLeilao = Leiloes::find($lote->idleilao);
            if ($modelLeilao) {
                $modelLeilao->encerrado = 1;
                $modelLeilao->save();
                $lote->encerrado = 1;
                $lote->save();
            }
            return true;
        }
        \Storage::disk('local')->delete('lotefuturo' . $lote->codigo);
        Log::info('Abertura de leilões: Lote ainda não será aberto ' . $lote->codigo . '=> Abertura em ' . date('d/m/Y H:i:s', $dataInicial));
        return false;
    }

    private static function getTimezoneByUF($uf)
    {
        // Mapeia os estados brasileiros para seus respectivos fusos horários
        $timezones = [
            'AC' => 'America/Rio_Branco',
            'AL' => 'America/Maceio',
            'AP' => 'America/Belem',
            'AM' => 'America/Manaus',
            'BA' => 'America/Bahia',
            'CE' => 'America/Fortaleza',
            'DF' => 'America/Sao_Paulo',
            'ES' => 'America/Sao_Paulo',
            'GO' => 'America/Sao_Paulo',
            'MA' => 'America/Fortaleza',
            'MT' => 'America/Cuiaba',
            'MS' => 'America/Campo_Grande',
            'MG' => 'America/Sao_Paulo',
            'PA' => 'America/Belem',
            'PB' => 'America/Fortaleza',
            'PR' => 'America/Sao_Paulo',
            'PE' => 'America/Recife',
            'PI' => 'America/Fortaleza',
            'RJ' => 'America/Sao_Paulo',
            'RN' => 'America/Fortaleza',
            'RS' => 'America/Sao_Paulo',
            'RO' => 'America/Porto_Velho',
            'RR' => 'America/Boa_Vista',
            'SC' => 'America/Sao_Paulo',
            'SP' => 'America/Sao_Paulo',
            'SE' => 'America/Maceio',
            'TO' => 'America/Araguaina',
        ];

        return $timezones[$uf] ?? 'America/Sao_Paulo'; // Default para São Paulo caso o estado não seja encontrado
    }
}
