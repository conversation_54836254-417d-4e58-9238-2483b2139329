<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>Uživatelská příručka CKFinder</title>
	<link href="../../files/other/help.css" type="text/css" rel="stylesheet" />
	<script type="text/javascript" src="../../files/other/help.js"></script>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta name="robots" content="noindex, nofollow" />
</head>
<body>
	<h1>
		Tla<PERSON><PERSON>tko Nastavení</h1>
	<p>
		Tlačítko <strong>Nastavení (Settings)</strong>, kter<PERSON> je dostupné v
		<strong><a href="005.html"><PERSON>u nástrojů</a></strong> CKFinder otevře
		<strong>Panel nastavení</strong>, kde si můžete CKFinder nastavit a přizpůsobit.</p>
	<p>Obrázek níže představuje <strong>Panel nastavení</strong>,
		který je rozbalen při kliknutí na tlačítko panelu nástrojů.</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_settings.png" width="580" height="233" alt="Panel nastavení CKFinder" />&nbsp;</p>
	<p>
		Všechna nastavení jsou automaticky uložena pomocí "cookies" v internetovém prohlížeči.
		"Cookies" jsou malé soubory, které ukládají soukromé informace o nastavení
		pro určité stránky na Vašem počítači.</p>
	<p>
		Abyste zavřeli (sbalili) <strong>Panel nástrojů</strong>, stiskněte tlačítko
		<strong>Zavřít (Close)</strong> nebo znovu klikněte na tlačítko <strong>Nastavení (Settings)</strong>.</p>
	<h2>
		Možnosti nastavení CKFinder</h2>
	<p>
		Všechny možnosti nastavení se vztahují k <strong><a href="004.html">Panelu složek</a>
		</strong> a kontrolují způsob, jakým jsou soubory v CKFinder zobrazeny.
		<strong>Panel souborů</strong> okamžitě zareaguje na změny v
		<strong>Panelu nastavení</strong>.</p>
	<h3>
		Zobrazení (View)</h3>
	<p>
		Nastaví režim zobrazení v <strong>Panelu souborů</strong>:</p>
	<ul>
		<li><strong>Náhled (Thumbnails)</strong> &ndash; tento režim zobrazí každý soubor v
			rámečku. Pro obrázky bude zobrazen uvnitř rámečku zobrazen
			malý náhled. Pro ostatní soubory bude místo toho dostupná
			ikona.</li>
		<li><strong>Seznam (List)</strong> &ndash; tento režim zobrazí všechny soubory v seznamu.
			V tomto režimu nejsou dostupné žádné náhledy.</li>
	</ul>
	<h3>
		Zobrazit (Display)</h3>
	<p>
		Nastaví množství informací, které jsou dostupné v <strong>Panelu souborů</strong>.
		Následující možnosti mohou být zapnuty či vypnuty:</p>
	<ul>
		<li><strong>Název (File Name)</strong> &ndash; zobrazí název souboru spolu s jeho příponou.</li>
		<li><strong>Datum (Date)</strong> &ndash; zobrazí poslední datum změny souboru.</li>
		<li><strong>Velikost (File Size)</strong> &ndash; zobrazí velikost souboru v kilobajtech.</li>
	</ul>
	<p>Pokud používáte režim zobrazení <strong>Náhled</strong>, můžete odškrtnout všechny
		možnosti. V režimu <strong>Seznam</strong> bude název souboru vždycky zobrazen.</p>
	<p>Obrázek níže představuje různé možnosti zobrazení tak, jak jsou zobrazeny v režimu
		<strong>Náhled</strong>.</p>
	<table align="center" cellpadding="0" cellspacing="0">
		<tr>
			<td valign="top" style="padding-left: 10px">
				<img src="../../files/images/CKFinder_file_display_04.png" width="122" height="112" alt="Soubor zobrazený v režimu Náhled bez názvu souboru, velikosti a data změny" /></td>
			<td valign="top" style="padding-right: 10px; padding-left: 10px">
				<img src="../../files/images/CKFinder_file_display_03.png" width="122" height="128" alt="Soubor zobrazený v režimu Náhled pouze s názvem souboru" /></td>
			<td valign="top" style="padding-right: 10px; padding-left: 10px">
				<img src="../../files/images/CKFinder_file_display_02.png" width="122" height="142" alt="Soubor zobrazený v režimu Náhled s názvem souboru a datem změny" /></td>
			<td valign="top" style="padding-right: 10px">
				<img src="../../files/images/CKFinder_file_display_01.png" width="122" height="158" alt="Soubor zobrazený v režimu Náhled s názvm souboru, velikostí a datem změny" /></td>
		</tr>
	</table>
	<h3>
		Seřazení (Sorting)</h3>
	<p>
		Nastaví pořadí, v kterém budou soubory zobrazeny. Jsou dostupné následující možnosti:</p>
	<ul>
		<li><strong>Podle názvu (by File Name)</strong> &ndash; seřadí soubory abecedně podle jejich názvu.</li>
		<li><strong>Podle data (by Data)</strong> &ndash; seřadí soubory podle posledního data změny,
			nejnovější je zobrazen první.</li>
		<li><strong>Podle velikosti (by Size)</strong> &ndash; seřadí soubory podle jejich velikosti, největší je zobrazen
			první.</li>
		<li><strong>Podle přípony (by Extension)</strong> &ndash; seřadí soubory nejdříve abecedně podle jejich
			přípony a pak abecedně podle jejich názvů.</li>
	</ul>
</body>
</html>
