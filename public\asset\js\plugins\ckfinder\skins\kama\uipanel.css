/*
Copyright (c) 2003-2011, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://cksource.com/license/ckfinder
*/

html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,a,abbr,acronym,address,big,cite,code,del,dfn,em,font,img,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,b,u,i,center,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td{margin:0;padding:0;border:0;outline:0;font-size:100%;vertical-align:baseline;background:transparent;}body{line-height:1;}ol,ul{list-style:none;}blockquote,q{quotes:none;}blockquote:before,blockquote:after,q:before,q:after{content:'';content:none;}:focus{outline:0;}ins{text-decoration:none;}del{text-decoration:line-through;}table{border-collapse:collapse;border-spacing:0;}body{font:9pt/1.5em Arial,Helvetica,sans-serif;}pre,code,tt{font:1em/1.5em 'Andale Mono','Lucida Console',monospace;}h1,h2,h3,h4,h5,h6,b,strong{font-weight:bold;}em,i,dfn{font-style:italic;}dfn{font-weight:bold;}p,code,pre,kbd{margin:0 0 1.5em 0;}blockquote{margin:0 1.5em 1.5em 1.5em;}li ul,li ol{margin:0 1.5em;}ul,ol{margin:0 1.5em 1.5em 1.5em;}ul,ul li{list-style-type:disc;}ol,ol li{list-style-type:decimal;}dl{margin:0 0 1.5em 0;}dl dt{font-weight:bold;}dd{margin-left:1.5em;}table{margin-bottom:1.4em;width:100%;}th{font-weight:bold;}th,td,caption{padding:4px 10px 4px 5px;}tfoot{font-style:italic;}sup,sub{line-height:0;}abbr,acronym{border-bottom:1px dotted;}address{margin:0 0 1.5em;font-style:italic;}del{text-decoration:line-through;}pre{margin:1.5em 0;white-space:pre;}img.centered,.aligncenter,div.aligncenter{display:block;margin-left:auto;margin-right:auto;}img.alignright{display:inline;}img.alignleft{display:inline;}.alignright{float:right;margin-left:10px;}.alignleft{float:left;margin-right:10px;}.clearfix::after,.group::after{content:".";display:block;height:0;clear:both;visibility:hidden;}*:first-child+html .clearfix{zoom:1;display:block;}* html .clearfix{zoom:1;display:block;}*:first-child+html .group{zoom:1;display:block;}* html .group{zoom:1;display:block;}.cke_compatibility a,.cke_compatibility a:hover,.cke_compatibility a:active,.cke_compatibility a:visited{color:black;text-decoration:none;}.cke_compatibility,.cke_compatibility td,.cke_compatibility td *{font-size:11px;font-family:'Microsoft Sans Serif',Tahoma,Arial,Verdana,Sans-Serif;line-height:1;}.cke_compatibility tbody{border:0;}.cke_compatibility .cke_accessibility{display:none;}.cke_compatibility table{width:auto;}.cke_compatibility{background-color:#FFF;}.cke_compatibility *{color:black;height:auto;border-collapse:collapse;padding:0;margin:0;vertical-align:baseline;white-space:nowrap;}.cke_compatibility pre,.cke_compatibility textarea{white-space:pre;}.cke_compatibility code,.cke_compatibility font{white-space:normal;}.cke_skin_kama .cke_contextmenu{padding:2px;}.cke_skin_kama .cke_menuitem a{display:block;}.cke_skin_kama .cke_menuitem span{cursor:default;}.cke_skin_kama .cke_menuitem a:hover,.cke_skin_kama .cke_menuitem a:focus,.cke_skin_kama .cke_menuitem a:active{background-color:#D3D3D3;display:block;}.cke_skin_kama .cke_menuitem a.cke_disabled:hover,.cke_skin_kama .cke_menuitem a.cke_disabled:focus,.cke_skin_kama .cke_menuitem a.cke_disabled:active{background-color:transparent!important;}.cke_skin_kama .cke_menuitem .cke_icon{width:16px;height:16px;float:left;}.cke_skin_kama .cke_menuitem .cke_disabled .cke_icon{filter:alpha(opacity=70);opacity:.70;}.cke_skin_kama .cke_menuitem .cke_icon_wrapper{background-color:#D3D3D3;border:solid 4px #D3D3D3;width:16px;height:16px;float:left;filter:alpha(opacity=70);opacity:.70;clear:both;}.cke_rtl .cke_skin_kama .cke_menuitem .cke_icon_wrapper{float:right;}.cke_skin_kama .cke_menuitem a:hover .cke_icon_wrapper,.cke_skin_kama .cke_menuitem a:focus .cke_icon_wrapper,.cke_skin_kama .cke_menuitem a:active .cke_icon_wrapper{background-color:#9d9d9d;border:solid 4px #9d9d9d;filter:alpha(opacity=70);opacity:.70;}.cke_skin_kama .cke_menuitem a:hover.cke_disabled .cke_icon_wrapper,.cke_skin_kama .cke_menuitem a:focus.cke_disabled .cke_icon_wrapper,.cke_skin_kama .cke_menuitem a:active.cke_disabled .cke_icon_wrapper{background-color:#D3D3D3;border:solid 4px #D3D3D3;}.cke_skin_kama .cke_menuitem .cke_label{display:block;padding-right:3px;padding-top:5px;padding-left:4px;height:19px;margin-left:24px;background-color:#fff;}
.cke_skin_kama .cke_frameLoaded .cke_menuitem .cke_label{filter:alpha(opacity=70);opacity:.70;}.cke_rtl .cke_skin_kama .cke_menuitem .cke_label{padding-right:0;margin-left:0;padding-left:3px;margin-right:28px;}.cke_skin_kama .cke_menuitem a.cke_disabled .cke_label{filter:alpha(opacity=30);opacity:.30;}.cke_skin_kama .cke_menuitem a:hover .cke_label,.cke_skin_kama .cke_menuitem a:focus .cke_label,.cke_skin_kama .cke_menuitem a:active .cke_label{background-color:#D3D3D3;}.cke_skin_kama .cke_menuitem a.cke_disabled:hover .cke_label,.cke_skin_kama .cke_menuitem a.cke_disabled:focus .cke_label,.cke_skin_kama .cke_menuitem a.cke_disabled:active .cke_label{background-color:transparent;}.cke_skin_kama .cke_menuseparator{background-color:#D3D3D3;height:2px;filter:alpha(opacity=70);opacity:.70;_font-size:0;}.cke_skin_kama .cke_menuarrow{background-image:url(images/sprites.png);_background-image:url(images/sprites_ie6.png);background-position:0 -1411px;background-repeat:no-repeat;height:5px;width:3px;float:right;margin-right:2px;margin-top:3px;}.cke_rtl .cke_skin_kama .cke_menuarrow{float:left;margin-right:0;margin-left:2px;background-image:url(images/sprites.png);_background-image:url(images/sprites_ie6.png);background-position:0 -1390px;background-repeat:no-repeat;}.cke_browser_ie.cke_ltr .cke_skin_kama .cke_menuarrow{position:absolute;right:2px;}.cke_browser_ie.cke_rtl .cke_skin_kama .cke_menuarrow{position:absolute;left:2px;}.cke_skin_kama .cke_panel{border:1px solid #8f8f73;background-color:#fff;width:120px;height:100px;overflow:hidden;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px;}.cke_skin_kama .cke_contextmenu{margin:0;}.cke_skin_kama .cke_panel iframe{width:100%;height:100%;}html.cke_skin_kama_container{overflow:auto;overflow-x:hidden;}body.cke_panel_frame{overflow:auto;overflow-x:hidden;}ul.cke_panel_list{list-style-type:none;margin:3px;padding:0;white-space:nowrap;}li.cke_panel_listItem{margin:0;}.cke_panel_listItem a{padding:2px;display:block;border:1px solid #fff;color:inherit;text-decoration:none;overflow:hidden;text-overflow:ellipsis;}* html .cke_panel_listItem a{width:100%;color:#000;}*:first-child+html .cke_panel_listItem a{color:#000;}.cke_panel_listItem.cke_selected a{border:1px solid #ccc;background-color:#e9f5ff;}.cke_panel_listItem a:hover,.cke_panel_listItem a:focus,.cke_panel_listItem a:active{border-color:#316ac5;background-color:#dff1ff;}.cke_panel_grouptitle{font-size:11px;font-family:'Microsoft Sans Serif',Tahoma,Arial,Verdana,Sans-Serif;font-weight:bold;white-space:nowrap;background-color:#dcdcdc;color:#000;margin:0;padding:3px;}html.cke_skin_kama_container{visibility:visible;}.cke_skin_kama .ckf_uploadButtons span.cke_dialog_ui_button{width:110px;padding:2px;}.cke_skin_kama .ckf_uploadButtons .cke_dialog_ui_button{margin:3px 0;}.cke_skin_kama .ckf_uploadButtons a.cke_dialog_ui_button_ok span,.cke_skin_kama .ckf_uploadButtons a.cke_dialog_ui_button_cancel span{background-image:none;padding:2px;}.cke_skin_kama .cke_button_TruncateBasket .cke_icon{background:url('images/toolbar/clear_basket.gif') no-repeat 0 0;}.cke_skin_kama .cke_button_settings .cke_icon{background:url('images/toolbar/settings.gif') no-repeat 0 0;}.cke_skin_kama .cke_button_refresh .cke_icon{background:url('images/toolbar/refresh.gif') no-repeat 0 0;}.cke_skin_kama .cke_button_upload .cke_icon{background:url('images/toolbar/add.gif') no-repeat 0 0;}.cke_skin_kama .cke_button_help .cke_icon{background:url('images/toolbar/help.gif') no-repeat 0 0;}.cke_skin_kama .cke_button_downloadFile .cke_icon{background:url('images/toolbar/download.gif') no-repeat 0 0;}.cke_skin_kama .cke_button_viewFile .cke_icon{background:url('images/toolbar/view.gif') no-repeat 0 0;}.cke_skin_kama .cke_button_deleteFile .cke_icon,.cke_skin_kama .cke_button_deleteFiles .cke_icon{background:url('images/toolbar/delete.gif') no-repeat 0 0;}.cke_skin_kama .cke_button_removeFolder .cke_icon{background:url('images/toolbar/delete.gif') no-repeat 0 0;}.cke_skin_kama .cke_button_maximize .cke_icon{background:url('images/toolbar/maximize.gif') no-repeat 0 0;}
#folders_view .folder_tree li a{background:url('images/ckffolder.gif') 3px 0 no-repeat;}.cke_rtl #folders_view .folder_tree li a{background-position:right;}#folders_view .folder_tree li.closable a{background-image:url('images/ckffolderopened.gif');}#folders_view .folder_tree li.openable a{background-image:url('images/ckffolder.gif');}#folders_view .folder_tree li.nochildren a{background-image:url('images/ckffolder.gif');}#folders_view .folder_tree li a.processing{background-image:url('images/loaders/16x16.gif');}#folders_view .folder_tree li.closable .expander{background-image:url('images/ckfminus.gif');}#folders_view .folder_tree li.openable .expander{background-image:url('images/ckfplus.gif');}#files_view .files_thumbnails .processing div.image{background:url("images/loaders/32x32.gif") 50% 50% no-repeat;}
