<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use App\Lotes;
use App\Leiloes;
use App\LotesImagens;
use App\LotesAnexos;
use App\Estados;
use Illuminate\Support\Facades\Log;
use Auth;

class LotesParceirosController extends Controller
{
    private $layout = 'admin.lotes.index-parceiro';
    /**
     * Show the profile for the given user.
     *
     * @param  int  $id
     * @return View
     */
    public function show($id)
    {
        // $lotes = Lotes::findOrFail($id);
        // echo $lotes->idestado;die;
        return view($this->layout, ['user' => Lotes::findOrFail($id)]);
    }
    /**
     * List all profiles.
     *
     * @param  int  $id
     * @return redirect()
     */
    public function index()
    {
        $vet = Leiloes::all();
        
        return view($this->layout, ['cads' => $vet]);
    }

    /**
     * List all profiles.
     *
     * @param  Request  $request
     * @return redirect()
     */

    public function search(Request $request)
    {
        $input = $request->all();
        $cadastros = new Lotes();
        $nome = trim($input['nome']);
        $cpfCnpj = trim($input['cpf_cnpj']);
        $status = (int) $input['status'];
        $dataInicial = trim($input['data_inicial']);
        $dataFinal = trim($input['data_final']);

        if ($nome != '') {
            $cadastros = $cadastros->where('nome', 'like', '%' . $nome . '%')
                                ->orWhere('razao_social', 'like ', '%' . $nome . '%');
        }

        if ($cpfCnpj != '') {
            $cadastros = $cadastros->where('cpf', 'like', '%' . $cpfCnpj . '%')
                                ->orWhere('cnpj', 'like ', '%' . $cpfCnpj . '%');
        }

        if ($status != 10) {
            $cadastros = $cadastros->where('status', '=',  $status );
        }

        if ($dataInicial != '' && $dataFinal != '') {
            $dataInicial = explode('/', $dataInicial);
            $dataFinal = explode('/', $dataFinal);
            $dataInicial = $dataInicial[2] . '-' . $dataInicial[1] . '-' . $dataInicial[0];
            $dataFinal = $dataFinal[2] . '-' . $dataFinal[1] . '-' . $dataFinal[0];
            $cadastros = $cadastros->whereBetween('data_cadastro', [$dataInicial, $dataFinal]);
        }
        $cadastros = $cadastros->get();
        
        return view('admin.cads.index', ['cads' => $cadastros, 'input' => $input]);
    }

    public function getLotes($id)
    {
        $lotes = Lotes::with('leilao')->where('idleilao', '=', (int) $id)->orderBy('codigo', 'asc')->get();
        $leilaoData = $lotes->count() > 0 ? $lotes[0]->leilao : Leiloes::findOrFail($id);
        $dataEncerramento = false;
        $countLote = $lotes->count();
        if ($countLote > 0) {
            $lastIndex = $countLote - 1;
            $dataEncerramento  = ($leilaoData->delay_encerramento_lotes*60) + strtotime($lotes[$lastIndex]->leilao2_data_final . ' ' . $lotes[$lastIndex]->leilao2_hora_final);
            $dataEncerramento = date('d/m/Y H:i', $dataEncerramento);
        }
        $leilao = stripslashes(@$lotes[0]->titulo);
        return view($this->layout, ['lotes' => $lotes, 'cidades' => [], 'leilaoData' => $leilaoData, 'idleilao' => @$lotes[0]->idleilao ?? $id, 'leilao' => $leilao, 'dataEncerramento' => $dataEncerramento]);
    }

    /**
     * Update the profile for the given user.
     *
     * @param  int  $id
     * @return redirect()
     */
    public function save(Request $request)
    {
        $input = $request->all();
        
        $cadastros = new Lotes();
        $input['codigo'] = (int) $input['codigo'];
        
        if ($input['codigo'] > 0) {
            $cadastros = Lotes::find($input['codigo']);
        }
        $leilao = Leiloes::findOrFail($input['idleilao']);
        $cadastros->idleilao = $leilao->codigo;
        $cadastros->categoria = $input['categoria'];
        
        $cadastros->subcategoria = $input['subcategoria'];
        $cadastros->idestado = $input['idestado'];
        $input['idcidade'] = explode("|", $input['idcidade']);
        $cadastros->idcidade = $input['idcidade'][1];
        $cadastros->bairro = $input['bairro'];
        $cadastros->endereco = $input['endereco'];
        $cadastros->numero = $input['numero'];
        $cadastros->titulo = $input['titulo'];
        $cadastros->subtitulo = $input['subtitulo'];
        $cadastros->avaliacao = 0;
        $cadastros->min_venda = 0;
        $cadastros->debitos = 0;
        
        $cadastros->lance_data_1 = str_replace(",", ".", str_replace(".", "", $input['lance_data_1']));
        
        $cadastros->lance_data_2 = str_replace(",", ".", str_replace(".", "", $input['lance_data_2']));
        $cadastros->incremento = 0;
        
        $timeStampAbertura = \Carbon\Carbon::createFromFormat('d/m/Y H:i', $input['abertura'])->timestamp;
        
        $cadastros->abertura = $input['abertura'];
        $cadastros->fechamento = $input['fechamento'];
        $cadastros->num_processo = '';
        $cadastros->url_consulta = '';
        $cadastros->vara = '';
        $cadastros->juiz = '';
        $cadastros->comissao_leiloeiro = 0;
        $cadastros->nome_exequente = '';
        $cadastros->doc_exequente = '';
        $cadastros->nome_executado = '';
        $cadastros->doc_executado = '';
        $cadastros->nome_depositario = '';
        $cadastros->cpf_depositario = '';
        $cadastros->rg_depositario = '';
        $cadastros->edital = '';
        $cadastros->publicacao = '';
        $cadastros->doe = '';
        $cadastros->cda = '';
        $cadastros->chb = '';
        $cadastros->descricao = '';
        $cadastros->visitacao = '';
        $cadastros->compra_parcelada = '';
        $cadastros->mapa = '';
        $cadastros->lote_destaque = $input['lote_destaque'];
        $cadastros->requer_habilitacao = 2;//$input['requer_habilitacao'];
        $cadastros->exibir_valor = 2;//$input['exibir_valor'];
        
        $cadastros->encerrado = $input['encerrado'];//$leilao->tipo == '4' ? 1 : $leilao->encerrado;
        $cadastros->restrito = 2;//$input['restrito'];
        $cadastros->suspender = 2;
        $cadastros->ind_parcelada = 2;
        
        $cadastros->ind_status = 3;

        $cadastros->leilao2_data_final = $leilao->leilao2_data_final;
        $cadastros->leilao2_hora_final = $leilao->leilao2_hora_final;
        
        $cadastros->leilao2_data_inicial = $leilao->leilao2_data_inicial;
        $cadastros->leilao2_hora_inicial = $leilao->leilao2_hora_inicial;

        $cadastros->leilao_data_final = $leilao->leilao_data_final;
        $cadastros->leilao_hora_final = $leilao->leilao_hora_final;
        
        $cadastros->leilao_data_inicial = $leilao->leilao_data_inicial;
        $cadastros->leilao_hora_inicial = $leilao->leilao_hora_inicial;

        $ultimoLote = Lotes::where('idleilao', $leilao->codigo)->where('codigo', '!=', $cadastros->codigo)
                            ->orderBy('codigo', 'desc')
                            ->limit(1)
                            ->get();

        if ($ultimoLote->count() > 0) {
            $ultimoLote = $ultimoLote[0];
            $minEncerramentoProximosLotes = $leilao->delay_encerramento_lotes * 60;

            $strTimeDataFinal1 = strtotime($ultimoLote->leilao_data_final . ' ' . $ultimoLote->leilao_hora_final);
            $strTimeDataFinal2 = strtotime($ultimoLote->leilao2_data_final . ' ' . $ultimoLote->leilao2_hora_final);

            $strTimeDataFinal1 = $strTimeDataFinal1 + $minEncerramentoProximosLotes;
            $strTimeDataFinal2 = $strTimeDataFinal2 + $minEncerramentoProximosLotes;
            $loteDataFinal = date('Y-m-d', $strTimeDataFinal1);
            $loteHoraFinal = date('H:i:s', $strTimeDataFinal1);

            $lote2DataFinal = date('Y-m-d', $strTimeDataFinal2);
            $lote2HoraFinal = date('H:i:s', $strTimeDataFinal2);
            $loteFechamento = date('d/m/Y H:i', $strTimeDataFinal2);

            $cadastros->fechamento = $loteFechamento;
            $cadastros->leilao2_data_final = $lote2DataFinal;
            $cadastros->leilao2_hora_final = $lote2HoraFinal;

            $cadastros->leilao_data_final = $loteDataFinal;
            $cadastros->leilao_hora_final = $loteHoraFinal;
            
            $cadastros->leilao_data_inicial = $leilao->leilao_data_inicial;
            $cadastros->leilao_hora_inicial = $leilao->leilao_hora_inicial;

            $cadastros->leilao2_data_inicial = $leilao->leilao2_data_inicial;
            $cadastros->leilao2_hora_inicial = $leilao->leilao2_hora_inicial;
        }

        $cadastros->metragem = '';
        $cadastros->dormitorios = 0;
        $cadastros->garagem = 0;
        $cadastros->banheiros = 0;
        $cadastros->situacao = 0 ;
        $cadastros->desc_condicao_pagamento = '';
        
        $cadastros->save();
        
        return redirect()->back()->with('success', 'Operação concluída.');
    }

    /**
     * Delete the profile for the given user.
     *
     * @param  int  $id
     * @return redirect()
     */
    public function delete($idleilao, $id)
    {
        $user = Auth::guard('admin')->user();
        $id = (int) $id;
        $cadastro = Lotes::findOrFail($id);
        $titulo = $cadastro->titulo;
        $cadastro->delete();
        Log::channel('leiloes')->info('Lote ' . $id . ' [' . $titulo .'] excluído por ' . $user->nome . ' [' . $user->email . ']');
        return redirect('lotes/leilao/parceiro/' . $idleilao)->with('success', 'Operação concluída.');
    }

    /**
     * Show the profile for the given user.
     *
     * @param  int  $id
     * @return View
     */
    public function get($id)
    {
        $id = (int) $id;
        $lote = Lotes::with('imagensLote', 'leilao')->findOrFail($id);
        if ($lote['leilao']->parceiro != true) {
            return abort(404);
        }
        $lote = $lote->toArray();
        $lotes = Lotes::where('idleilao', '=', (int) $lote['idleilao'])->get();
        $cidades = Estados::with('cidades')->find($lote['idestado']);
        $leilao = stripslashes(@$lotes['titulo']);
        return view($this->layout, ['vet' => $lote, 'lotes' => $lotes, 
                                         'idleilao' => @$lotes[0]->idleilao, 
                                         'cidades' => $cidades->cidades,
                                         'idcidade' => $lote['idcidade'],
                                         'idcategoria' => $lote['categoria'],
                                         'idsubcategoria' => $lote['subcategoria'],
                                         'leilao' => $leilao]);
    }

    /**
     * Exclui a imagem do lote
     */
    public function excluirImagem($id)
    {
        $imagem = LotesImagens::findOrFail($id);
        $idLote = $imagem->idlote;
        $pathImagem = storage_path() . '/app/public/imagens/' . $imagem->arquivo;
        if (file_exists($pathImagem)) {
            unlink($pathImagem);
        }
        $imagem->delete();
        return redirect('/lotes/' . $idLote )->with('success', 'Operação concluída.');
    }

    public function getImages($id)
    {
        $lote = Lotes::with('leilao')->find($id);
        $imagens = $lote->imagensLote;
        return view('admin.lotes.ordenar-imagens-lote', ['lote' => $lote, 'imagens' => $imagens]);
    }

    public function getAnexos($id)
    {
        $lote = Lotes::find($id);
        $anexos = $lote->anexosLote;
        return view('admin.lotes.lotes-anexos', ['lote' => $lote, 'anexos' => $anexos]);
    }

    public function editarAnexo($idLote, $codigo)
    {
        $lote = Lotes::find($idLote);
        $anexos = $lote->anexosLote;
        $anexo = LotesAnexos::findOrFail($codigo);
        return view('admin.lotes.lotes-anexos', ['lote' => $lote, 'anexos' => $anexos, 'anexo' => $anexo]);
    }

    public function updateOrderImage(Request $request)
    {
        $input = $request->all();
        $updateRecordsArray = $input['recordsArray'];
        if (count($updateRecordsArray) > 0) {
            $ind = 1;
            foreach ($updateRecordsArray as $recordIDValue)
            {
                $codigo = $recordIDValue;
                $imagem = LotesImagens::findOrFail($codigo);
                
                $imagem->ordem = $ind;
                $imagem->save();
                $ind = $ind + 1;	
            }
            if($ind == (count($updateRecordsArray) + 1))
            {
                return 'Imagens ordenadas com sucesso!';
            }
        }
        return 'error';
    }

    public function removeAnexo($codigo)
    {
        $anexo = LotesAnexos::findOrFail($codigo);
        $arquivo = $anexo->arquivo;
        $anexo->delete();
        Storage::delete('documentos/leilao' . $arquivo);
        return redirect('/lote/' . $anexo->idlote . '/anexos');
    }

    public function salvaAnexo(Request $request)
    {
        $input = $request->all();
        $originalName = '';
        $anexos = new LotesAnexos;
        $codigo = (int) $request->codigo;
        if ($codigo > 0) {
            $anexos = LotesAnexos::find($codigo);
        }
        if ($request->anexos) {
            $originalName = $request->anexos->getClientOriginalName();
            $ext = $request->anexos->getClientOriginalExtension();
            $originalName = str_replace([' ', '/', '\\', '(', ')', '=', '+', '_'], '-', $originalName);
            $originalName = str_replace('.'.$ext, '', $originalName);
            $originalName = $originalName . '-' . md5($originalName.time().$codigo) . '.' . $ext;
            $request->anexos->storeAs('documentos/leilao', $originalName);
        }
        $nome = trim ($request->nome);
        $nome  = $nome  == '' ? $originalName : $nome;//md5($nome . $codigo);
        $anexos->idlote = (int) $request->idlote;
        if ($originalName != '') {
            $anexos->arquivo = $originalName;
        }
        $anexos->nome = $nome;
        $anexos->save();
        
        return redirect('/lote/' . $anexos->idlote . '/anexos');
    }

    public function getSubcat(Request $request)
    {
        $id = $request->idcategoria;
        
        return view('admin.partials.combo-subcat', ['idcategoria' => $id]);
    }
}