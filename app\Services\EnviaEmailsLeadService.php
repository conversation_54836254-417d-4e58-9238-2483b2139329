<?php

namespace App\Services;

use App\Mail\CadastroUsuario;
use App\Mail\NotificaAdms;
use App\Utils\Notifications;
use Illuminate\Support\Facades\Mail;

class EnviaEmailsLeadService {

    public static function enviaEmails($lote, $nome, $cpf, $email, $telefone, $rendaBruta = '', $dataNascimento = '', $mensagem = '') {
        $rendaBruta = trim($rendaBruta);
        $dataNascimento = trim($dataNascimento);
        $mensagem  = trim ($mensagem);
        $mensagem = $mensagem == '' ? 'Nenhuma mensagem foi escrita pelo interessado' : $mensagem;
        $appurl = env('APP_URL');
        $urlLog = $appurl.'/leilao/lote/'.$lote->codigo.'/' . \Str::slug($lote->titulo);
        $url = '<a href="'.$urlLog.'">' . $lote->titulo  .'</a>';
        $texto = 'Acabamos de receber um interesse em um dos lotes do site. Abaixo os dados primários: <br />';
        $texto  .= '<strong>Nome</strong>: ' . $nome . '<br />';
        $texto  .= '<strong>CPF:</strong>: ' . $cpf . '<br />';
        $texto  .= '<strong>E-mail</strong>: ' . $email . '<br />';
        $texto  .= '<strong>Telefone</strong>: ' . $telefone . '<br />';
        if ($rendaBruta != '' && $dataNascimento != '') {
            $texto  .= '<strong>Renda bruta</strong>: ' . $rendaBruta . '<br />';
            $texto  .= '<strong>Data de nascimento</strong>: ' . $dataNascimento . '<br />';
        }
        $texto  .= '<strong>Lote</strong>: ' . $url . '<br />';
        $texto  .= '<strong>Mensagem</strong>: ' . $mensagem . '<br />';

        Mail::to('<EMAIL>')
        ->queue(new NotificaAdms('Temos um interessado', $texto));

        return true;
    }
}