<?php

namespace App\Routines;

use App\Lotes;
use App\Leiloes;
use App\Cadastros;
use App\ConfigEmail;
use App\Mail\ComunicaVencedor;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class EncerramentoLeilao
{
    public static function encerramento($lote, $cli = 0)
    {
        $leiloesToResolve = [];
        $lote = (int) $lote;
        $texto = ConfigEmail::where('email_tipo', 'resultado_leilao')->orderBy('id')->get();
        $strNome = '';
        $textBody = 'Prezado(a) Senhor(a) {{str_nome}},<br><br>';
        $textBody .= 'Temos a satisfação de intima-lo que seu lance foi o vencedor do leilão em apreço.<br><br>';
        $textBody .= 'Dentro de instantes V.Sa. receberá maiores informações sobre o procedimento a ser seguido, bem como as informações para pagamento do valor da arrematação e da comissão do leiloeiro.<br><br>';
        $textBody .= 'Atenciosamente,<br>';
        $textBody .= 'Balbino Leilões - Responsabilidade Social e Ambiental<br>';
        $textBody .= 'Telefone e whatsaap: (11) 4020-1694<br />';
        $textBody .= 'Rua 2, JK, n° 264, Cuiabá - MT<br>';
        $textBody .= 'CEP: 78068-340<br>';
        $textBody .= 'Rua Travessa Oceânica, 171, Cachoeira do Bom Jesus, Florianópolis SC<br>';
        $textBody .= 'CEP: 88056-481<br>';
        $textBody .= '<EMAIL> - www.balbinoleiloes.com.br';

        $textoGravado = $textBody;
        $textoGravado1 = 'Prezado(a) Senhor(a) {{str_nome}},<br><br>';
        $textoGravado1 .= 'Temos a satisfação de intima-lo que sua Proposta de Compra será encaminhada para apreciação judicial.<br><br>';
        $textoGravado1 .= 'Comunicaremos V.Sa. da manifestação judicial correspondente.<br><br>';
        $textoGravado1 .= 'Havendo aceitação judicial de sua Proposta de Compra, V.Sa. receberá maiores informações sobre o procedimento a ser seguido para formalizar a arrematação em apreço, bem como receberá informações para pagamento do valor da arrematação e da comissão do leiloeiro.<br><br>';
        $textoGravado1 .= 'Atenciosamente,<br>';
        $textoGravado1 .= 'Balbino Leilões - Responsabilidade Social e Ambiental<br>';
        $textoGravado1 .= 'Telefone e whatsaap: (11) 4020-1694<br />';
        $textoGravado1 .= 'Rua 2, JK, n° 264, Cuiabá - MT<br>';
        $textoGravado1 .= 'CEP: 78068-340<br>';
        $textoGravado1 .= 'Rua Travessa Oceânica, 171, Cachoeira do Bom Jesus, Florianópolis SC<br>';
        $textoGravado1 .= 'CEP: 88056-481<br>';
        $textoGravado1 .= '<EMAIL> - www.balbinoleiloes.com.br';

        $textoGravado = $texto[0]->texto;
        $textoGravado1 = $texto[1]->texto;

        Log::info('Encerramento de leilões: CRON - Iniciando');

        $qtdLeiloes = 0;

        $lotes = Lotes::with('maiorLance', 'maiorLanceParcelado')->where('encerrado', '=', 1);

        $encerramentoAutomatico = true;

        if ($lote > 0) {
            $lotes = $lotes->where('codigo', $lote);
            $encerramentoAutomatico = false;
        }

        $lotes = $lotes->get();

        foreach ($lotes as $lote) {
            // Define o fuso horário com base no estado do leilão
            if ($lote->num_processo) {
                $timezone = getFuso($lote->num_processo);
            } else {
                $timezone = self::getTimezoneByUF($lote->estado->uf);
            }
            date_default_timezone_set($timezone);

            $dataInicial = strtotime($lote->leilao2_data_final . ' ' . $lote->leilao2_hora_final);
            $primeiraPraca = strtotime($lote->leilao_data_final . ' ' . $lote->leilao_hora_final);
            $primeiraPraca = time() - $primeiraPraca <= 3 ? true : false;

            if ($primeiraPraca) {
                $dataInicial = strtotime($lote->leilao_data_final . ' ' . $lote->leilao_hora_final);
            }

            $modelLeilao = Leiloes::find($lote->idleilao);

            if ((int) $modelLeilao->leilao_data_tipo > 2) {
                $primeiraPraca = false;
            }

            // Se a data inicial 2 ainda for futura, coloca o leilão em futuro
            $data1 = strtotime($lote->leilao_data_final . ' ' . $lote->leilao_hora_final);
            $data2 = strtotime($lote->leilao2_data_inicial . ' ' . $lote->leilao2_hora_inicial);

            if ($data2 > time() && time() > $data1 && !isset($lote->getLancesComCadastrosAttribute()[0])) {
                $lotes = Lotes::where('codigo', $lote->codigo)->update(['encerrado' => 7]);
                $leiloesToResolve['leilao'][$lote->idleilao] = ['encerrado' => 7];

                \Storage::disk('local')->delete('lotefuturo' . $lote->codigo);
                \Storage::disk('local')->put('lotefuturo' . $lote->codigo, 1);
                usleep(800000);
                Log::info('Encerramento de leilão: Voltando o leilão ao status futuro. Segunda data inicial é maior que a data atual. Leilão' . $lote->idleilao);
                $qtdLeiloes++;
                continue;
            }

            $statusEncerrado = 6;
            $diffTime = $dataInicial - time();

            if ($diffTime <= 1) {
                if ($lote->encerrado != 1) {
                    continue;
                }

                if ($modelLeilao->tipo == 4 && $modelLeilao->usar_cronometro == 0) {
                    $statusEncerrado = 1;
                    continue;
                }

                if ($primeiraPraca && !isset($lote->getLancesComCadastrosAttribute()[0])) {
                    continue;
                }

                $lances = $lote->getLancesComCadastrosAttribute();

                // if ($primeiraPraca && count($lances) == count($lote->lancesParcelados)) {
                //     continue;
                // }

                if (isset($lances[0])) {
                    if ($lances[0]->tipo == 1) {
                        $filter = array_filter($lances, function ($item) {
                            return $item->tipo != 1;
                        });

                        if (count($filter)) {
                            $cadastro = Cadastros::find($lances[0]->idcadastro);
                            $strNome = trim($cadastro->nome) != '' ? $cadastro->nome : $cadastro->razao_social;

                            Log::info('#################################################################');
                            Log::info('Encerramento automático: ' . ($encerramentoAutomatico === true ? 'Sim' : 'Não'));
                            Log::info('Encerramento de leilão: Enviando email de proposta em análise. Nome: ' . $strNome . ". Lote " . $lote->codigo);
                            Log::info('Dados do lote: ');
                            Log::info('ID: ' . $lote->codigo);
                            Log::info('Titulo: ' . $lote->titulo);
                            Log::info('Data 1: ' . $lote->leilao_data_final . " " . $lote->leilao_hora_final);
                            Log::info('Data 2: ' . $lote->leilao2_data_final . " " . $lote->leilao2_hora_final);
                            Log::info('#################################################################');

                            $textoGravado2 = 'Prezado(a) {{str_nome}},<br><br>';
                            $textoGravado2 .= 'Agradecemos sua participação no leilão do Lote ' . $lote->titulo . ', realizado por meio da nossa plataforma.<br><br>';
                            $textoGravado2 .= 'Informamos que, ao término do período de lances, foi registrada uma proposta de arrematação na modalidade parcelada, com valor superior ao lance ofertado à vista.<br><br>';
                            $textoGravado2 .= 'Nos termos do artigo 895, §7º, do Código de Processo Civil, a preferência é conferida ao pagamento à vista. Contudo, diante da superioridade do valor ofertado na proposta parcelada, a sua aceitação dependerá de deliberação da autoridade competente (Juízo responsável ou Comitente, conforme o caso).<br><br>';
                            $textoGravado2 .= 'Dessa forma, o resultado do referido lote encontra-se em fase de análise, e todos os participantes serão devidamente notificados assim que houver decisão definitiva.<br><br>';
                            $textoGravado2 .= 'Permanecemos à disposição para eventuais esclarecimentos.<br><br>';
                            $textoGravado2 .= 'Atenciosamente,<br>';
                            $textoGravado2 .= 'Balbino Leilões - Responsabilidade Social e Ambiental<br>';
                            $textoGravado2 .= 'Telefone e whatsaap: (11) 4020-1694<br />';
                            $textoGravado2 .= 'Rua 2, JK, n° 264, Cuiabá - MT<br>';
                            $textoGravado2 .= 'CEP: 78068-340<br>';
                            $textoGravado2 .= 'Rua Travessa Oceânica, 171, Cachoeira do Bom Jesus, Florianópolis SC<br>';
                            $textoGravado2 .= 'CEP: 88056-481<br>';
                            $textoGravado2 .= '<EMAIL> - www.balbinoleiloes.com.br';

                            preg_match_all('/(\{\{str_nome\}\})/', $textoGravado2, $saida, PREG_PATTERN_ORDER);
                            $saida = array_unique($saida[0]);
                            $arr = array('{{str_nome}}' => $strNome);
                            foreach ($saida as $value) {
                                $var = $arr[$value];
                                $textoGravado2 = str_replace($value, $var, $textoGravado2);
                            }
                            $textBody = $textoGravado2;

                            Mail::to($cadastro->email)
                                ->queue(new ComunicaVencedor('Atualização sobre o Lote ' . $lote->codigo . ' – Proposta em Análise', $textBody));

                            $cadastro2 = Cadastros::find($filter[0]->idcadastro);
                            $strNome2 = trim($cadastro2->nome) != '' ? $cadastro2->nome : $cadastro2->razao_social;

                            preg_match_all('/(\{\{str_nome\}\})/', $textoGravado2, $saida, PREG_PATTERN_ORDER);
                            $saida = array_unique($saida[0]);
                            $arr = array('{{str_nome}}' => $strNome2);
                            foreach ($saida as $value) {
                                $var = $arr[$value];
                                $textoGravado2 = str_replace($value, $var, $textoGravado2);
                            }
                            $textBody = $textoGravado2;

                            Mail::to($cadastro2->email)
                                ->queue(new ComunicaVencedor('Atualização sobre o Lote ' . $lote->codigo . ' – Proposta em Análise', $textBody));
                        } else {
                            $cadastro = Cadastros::find($lances[0]->idcadastro);
                            $strNome = trim($cadastro->nome) != '' ? $cadastro->nome : $cadastro->razao_social;
                            Log::info('#################################################################');
                            Log::info('Encerramento automático: ' . ($encerramentoAutomatico === true ? 'Sim' : 'Não'));
                            Log::info('Encerramento de leilão: Eviando email ao vencedor Compra Parcelada. Nome: ' . $strNome . ". Lote " . $lote->codigo);
                            Log::info('Dados do lote: ');
                            Log::info('ID: ' . $lote->codigo);
                            Log::info('Titulo: ' . $lote->titulo);
                            Log::info('Data 1: ' . $lote->leilao_data_final . " " . $lote->leilao_hora_final);
                            Log::info('Data 2: ' . $lote->leilao2_data_final . " " . $lote->leilao2_hora_final);
                            Log::info('#################################################################');
                            preg_match_all('/(\{\{str_nome\}\})/', $textoGravado1, $saida, PREG_PATTERN_ORDER);
                            $saida = array_unique($saida[0]);
                            $arr = array('{{str_nome}}' => $strNome);
                            foreach ($saida as $value) {
                                $var = $arr[$value];
                                $textoGravado1 = str_replace($value, $var, $textoGravado1);
                            }
                            $textBody = $textoGravado1;

                            Mail::to($cadastro->email)
                                ->queue(new ComunicaVencedor('Proposta de Compra Encaminhada para Apreciação Judicial', $textBody));
                        }
                    } else {
                        $cadastro = Cadastros::find($lances[0]->idcadastro);
                        $strNome = trim($cadastro->nome) != '' ? $cadastro->nome : $cadastro->razao_social;
                        Log::info('#################################################################');
                        Log::info('Encerramento automático: ' . ($encerramentoAutomatico === true ? 'Sim' : 'Não'));
                        Log::info('Encerramento de leilão: Eviando email ao vencedor. Nome: ' . $strNome . ". Lote: " . $lote->codigo);
                        Log::info('Dados do lote: ');
                        Log::info('ID: ' . $lote->codigo);
                        Log::info('Titulo: ' . $lote->titulo);
                        Log::info('Data 1: ' . $lote->leilao_data_final . " " . $lote->leilao_hora_final);
                        Log::info('Data 2: ' . $lote->leilao2_data_final . " " . $lote->leilao2_hora_final);
                        Log::info('#################################################################');
                        preg_match_all('/(\{\{str_nome\}\})/', $textoGravado, $saida, PREG_PATTERN_ORDER);
                        $saida = array_unique($saida[0]);
                        $arr = array('{{str_nome}}' => $strNome);
                        foreach ($saida as $value) {
                            $var = $arr[$value];
                            $textoGravado = str_replace($value, $var, $textoGravado);
                        }
                        $textBody = $textoGravado;

                        Mail::to($cadastro->email)
                            ->queue(new ComunicaVencedor('Lance Vencedor', $textBody));
                    }
                }

                $maiorLance = $lote->getLancesComCadastrosAttribute();
                $maiorLanceParcelado = $lote->maiorLanceParcelado;
                $statusEncerrado = $maiorLance->count() > 0 ? 8 : 6;
                if ($statusEncerrado == 6) {
                    $statusEncerrado = $maiorLanceParcelado->count() > 0 ? 10 : 6;
                }
                $leiloesToResolve['leilao'][$lote->idleilao] = ['encerrado' => $statusEncerrado];
                $qtdLeiloes++;
                $lote->encerrado = $statusEncerrado;
                $lote->save();
                if (file_exists('/tmp/lote-encerra' . $lote->codigo)) {
                    unlink('/tmp/lote-encerra' . $lote->codigo);
                }
            }
        }

        // por fim, resolve o status dos leiloes
        self::resolveLeiloes($leiloesToResolve, $lote);

        Log::info('Encerramento de leilões: Fim. ' . $qtdLeiloes . ' leilões afetados');
    }

    private static function resolveLeiloes($leiloesToResolve = array(), $lote)
    {
        /*if (isset ($leiloesToResolve['leilao'])) {
            foreach ($leiloesToResolve['leilao'] as $key => $encerrado) {
                $leilao = Leiloes::with('lots')->find($key);
                $updateLeilao = true;

                if ($leilao['lots']->count() > 1) {
                    foreach($leilao['lots'] as $lote) {
                        if ($lote->encerrado == 7 || $lote->encerrado == 1) {
                            $updateLeilao = false;
                            break;
                        }
                    }
                }
                // se todos os lotes estão encerrados, hora de definir o status do leilão
                if ($updateLeilao == true) {
                    $leilao->encerrado = $encerrado['encerrado'];
                    $leilao->save();
                }
            }
        }*/
    }

    private static function getTimezoneByUF($uf)
    {
        // Mapeia os estados brasileiros para seus respectivos fusos horários
        $timezones = [
            'AC' => 'America/Rio_Branco',
            'AL' => 'America/Maceio',
            'AP' => 'America/Belem',
            'AM' => 'America/Manaus',
            'BA' => 'America/Bahia',
            'CE' => 'America/Fortaleza',
            'DF' => 'America/Sao_Paulo',
            'ES' => 'America/Sao_Paulo',
            'GO' => 'America/Sao_Paulo',
            'MA' => 'America/Fortaleza',
            'MT' => 'America/Cuiaba',
            'MS' => 'America/Campo_Grande',
            'MG' => 'America/Sao_Paulo',
            'PA' => 'America/Belem',
            'PB' => 'America/Fortaleza',
            'PR' => 'America/Sao_Paulo',
            'PE' => 'America/Recife',
            'PI' => 'America/Fortaleza',
            'RJ' => 'America/Sao_Paulo',
            'RN' => 'America/Fortaleza',
            'RS' => 'America/Sao_Paulo',
            'RO' => 'America/Porto_Velho',
            'RR' => 'America/Boa_Vista',
            'SC' => 'America/Sao_Paulo',
            'SP' => 'America/Sao_Paulo',
            'SE' => 'America/Maceio',
            'TO' => 'America/Araguaina',
        ];

        return $timezones[$uf] ?? 'America/Sao_Paulo'; // Default para São Paulo caso o estado não seja encontrado
    }
}
