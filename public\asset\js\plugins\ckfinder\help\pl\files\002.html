<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>CKFinder &mdash; Podręcznik Użytkownika</title>
	<link href="../../files/other/help.css" type="text/css" rel="stylesheet" />
	<script type="text/javascript" src="../../files/other/help.js"></script>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta name="robots" content="noindex, nofollow" />
</head>
<body>
	<h1>
		CKFinder &mdash; Interfejs Użytkownika</h1>
	<p>
		Interfejs programu CKFinder został zaprojektowany pod kątem przejrzystości, intuicyjności
		oraz łatwości użycia.  Większość poleceń jest łatwo dostępna przy użyciu menu
		kontekstowego i może zostać wykonana za pomocą jednego kliknięcia myszą.</p>
	<p>
		Jeśli na co dzień obsługujesz menedżery plików wbudowane w większość systemów operacyjnych
		dostępnych na komputerach PC czy laptopach, szybko się przekonasz, że korzystanie z menedżera
		plików w przeglądarce jest równie proste i intuicyjne.</p>
	<p>
		Poniżej znajduje się przykładowy zrzut ekranu przedstawiający interfejs CKFindera:</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_interface.png" alt="Interfejs użytkownika programu CKFinder" width="620" height="456" />&nbsp;</p>
	<ol>
		<li><strong><a href="003.html">Panel folderów</a></strong> &ndash; zawiera "drzewo folderów",
			po którym można nawigować. Foldery wykorzystywane są do organizowania i kategoryzowania
			zbioru plików.</li>
		<li><strong><a href="004.html">Panel plików</a></strong> &ndash; wyświetla listę plików
			dostępnych w wybranym folderze.</li>
		<li><strong><a href="005.html">Pasek narzędzi</a></strong> &ndash; zestaw przycisków, które
			po kliknięciu wykonują powiązane z nimi funkcje menedżera plików.</li>
		<li><strong><a href="010.html">Pasek statusu</a></strong> &ndash; obszar na dole interfejsu
			przeznaczony do wyświetlania informacji dotyczących wybranego pliku, całkowitej liczby
			plików w folderze itd.</li>
		<li><strong><a href="012.html">Menu kontekstowe</a></strong> &ndash; menu wyskakujące
			zawierające polecenia menedżera plików wykonujące operacje specyficzne dla określonego
			obiektu. Dostępność poleceń w menu kontekstowym zmienia się w zależności od wybranego
			obiektu.</li>
	</ol>
</body>
</html>
