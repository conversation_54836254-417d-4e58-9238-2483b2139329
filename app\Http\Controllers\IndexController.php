<?php

namespace App\Http\Controllers;

use App\Slides;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Services\BuscaLeiloesService;

class IndexController extends Controller
{
    public function index(Request $request)
    {
        $slides = Slides::all();

        $leiloes = leiloesDestaque();

        $strWhere = " AND (A.encerrado in(1, 7, 9, 11) ) and A.suspender = 2 AND (A.encerrado in(1, 7, 9, 11) ) and A.suspender = 2";
        $strWhereTodas = " AND (A.encerrado in(1, 7, 9, 11) ) and A.suspender = 2";
        $strWhereRetomados = "";
        $orderAberto = " order by grupo asc, praca asc ";
        $paramList = [
            "num_processo" => "",
            "cod_referencia" => "",
            "subtitulo" => "%%",
            "titulo" => "%%",
            "descricao" => "%%"
        ];

        $strWhereEdicao = " AND A.tipo in(1,2,3,4,5,6) and A<PERSON>en<PERSON>rado in(9) and A<PERSON>suspender = 2 ";

        $imoveis = BuscaLeiloesService::lotesContador('imoveis', $strWhere, $strWhereTodas, $strWhereRetomados, $orderAberto, $paramList, $strWhereEdicao);
        $veiculos = BuscaLeiloesService::lotesContador('veiculos', $strWhere, $strWhereTodas, $strWhereRetomados, $orderAberto, $paramList, $strWhereEdicao);
        $diversos = BuscaLeiloesService::lotesContador('diversos', $strWhere, $strWhereTodas, $strWhereRetomados, $orderAberto, $paramList, $strWhereEdicao);

        return view('site.destaque', [
            'search' => '',
            'slides' => $slides,
            'leiloes' => $leiloes,
            'qtd_imoveis' => $imoveis,
            'qtd_veiculos' => $veiculos,
            'qtd_diversos' => $diversos,
            'input' => $request->all(),
            'request' => $request
        ]);
    }
}
