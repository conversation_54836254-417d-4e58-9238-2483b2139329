<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>CKFinder User's Guide</title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<link href="../../files/other/help.css" type="text/css" rel="stylesheet" />
	<script type="text/javascript" src="../../files/other/help.js"></script>
	<meta name="robots" content="noindex, nofollow" />
</head>
<body>
	<h1>
	Introducci&oacute;n a la interface de CKFinder</h1>
	<p>
		La Interface de CKFinder, ha sido dise&ntilde;ada para ser clara, familiar, f&aacute;cil de aprender y usar para nuestros usuarios finales. La mayor&iacute;a de las caracteristicas pueden ser usadas con movimientos del mouse y clicks en menus de contexto.</p>
	<p>
		La siguiente imagen, es un screenshot de CKFinder:	</p>
	<p style="text-align: center">
		<img src="images/001.png" alt="CKFinder Screenshot" height="404" width="622" />
	</p>
	<ol>
		<li><a href="003.html">P&aacute;nel de Carpetas </a>: contiene la &quot;vista de arbol&quot; de las carpetas donde se puede navegar, las carpetas son formas de organizar mejor los archivos.</li>
		<li><a href="004.html">P&aacute;nel de Archivos</a>: Muestra los archivos disponibles en la carpeta seleccionada.</li>
		<li><a href="005.html">Barra de Herramientas</a>: Es una serie de botones que pueden ser pulsados para ejecutar r&aacute;pidamente funciones espec&iacute;ficas.</li>
		<li><a href="010.html">Barra de Status </a>: Es un espacio utilizado para desplegar informaci&oacute;n referente al archivo seleccionado, el n&uacute;mero total de archivos en una carpeta etc.</li>
		<li><a href="012.html">Men&uacute; contextual </a>: Una serie de botones que pueden ser usados para ejecutar tareas espec&iacute;ficas en el objeto sobre el cual se ha dado un click. Las opciones disponibles cambian dinamicamente dependiendo del tipo del objeto al cual se le dio un click.</li>
	</ol>
</body>
</html>
