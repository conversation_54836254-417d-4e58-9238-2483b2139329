<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Lotes;
use App\Lances;
use App\LancesParcelados;
use Auth;

class InstitucionalController extends Controller
{
    public function index(Request $request)
    {
        $siteAddr = rtrim(str_replace([':8000', 'www.'], '', $request->url()), '/') . '/';
        $siteHost = rtrim(str_replace([':8000', 'www.'], '', $request->getSchemeAndHttpHost()), '/') . '/';
        
        $title = str_replace([$siteHost], '', $siteAddr);
        $title = trim($title, "/");
        $link = $title == '' ? 'quem-somos': $title;
        $text = getInstitutionalText($link);
        
        $titlePage = strtoupper(str_replace('-', ' ', $link));
        return view('site.institucional.institucional', ['title' => $titlePage, 'text' => $text, 'link' => $link]);

    }
}